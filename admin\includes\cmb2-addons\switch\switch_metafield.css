/* switch */
.cmb2-switch {
    margin-right: 15px;
    margin-bottom: 5px;
    float: left
}

.cmb2-switch label {
    cursor: pointer
}

.cmb2-switch input {
    display: none
}

.cmb2-enable, .cmb2-disable {
    background: #F5F5F5;
    color: #95999d;
    border: 1px solid #bfbfbf;
    display: block;
    float: left
}

.cmb2-enable {
    border-radius: 3px 0 0 3px;
    border-right: 0 none
}

.cmb2-disable {
    border-left: 0 none;
    border-radius: 0 3px 3px 0
}

.cmb2-enable span, .cmb2-disable span {
    line-height: 30px;
    display: block;
    font-weight: 400;
    white-space: nowrap;
    padding: 0 10px
}

.cmb2-disable.selected {
    background-color: #bfbfbf;
    background-image: none;
    border-color: #bfbfbf;
    color: #fff
}

.cmb2-enable.selected {
    background-color: #005077;
    background-image: none;
    border-color: #005077;
    box-shadow: none;
    color: #fff
}