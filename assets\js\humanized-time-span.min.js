function humanized_time_span(a,b,c,d){function e(){for(var a=0;a<c[j].length;a++)if(null==c[j][a].ceiling||i<=c[j][a].ceiling)return c[j][a];return null}function f(){for(var a=i,b={},c=0;c<d.length;c++){var e=Math.floor(a/d[c][0]);a-=d[c][0]*e,b[d[c][1]]=e}return b}function g(a){var b=f();return h(a.text.replace(/\$(\w+)/g,function(){return b[arguments[1]]}),b)}function h(a,b){for(var c in b)if(1==b[c]){var d=new RegExp("\\b"+c+"\\b");a=a.replace(d,function(){return arguments[0].replace(/s\b/g,"")})}return a}c=c||{past:[{ceiling:60,text:"$seconds seconds"},{ceiling:3600,text:"$minutes minutes"},{ceiling:86400,text:"$hours hours"},{ceiling:604800,text:"$days days"},{ceiling:2592e3,text:"$weeks weeks"},{ceiling:31556926,text:"$months months"},{ceiling:null,text:"$years years"}],future:[{ceiling:60,text:"$seconds seconds"},{ceiling:3600,text:"$minutes minutes"},{ceiling:86400,text:"$hours hours"},{ceiling:604800,text:"$days days"},{ceiling:2592e3,text:"$weeks weeks"},{ceiling:31556926,text:"$months months"},{ceiling:null,text:"$years years"}]},d=d||[[31556926,"years"],[2629744,"months"],[604800,"weeks"],[86400,"days"],[3600,"hours"],[60,"minutes"],[1,"seconds"]],a=new Date(a),b=b?new Date(b):new Date;var i=(b-a)/1e3,j="past";return i<0&&(j="future",i=0-i),g(e())}