var WCCT={};WCCT.Helpers={},WCCT.Views={},WCCT.Events={},_.extend(WCCT.Events,Backbone.Events),WCCT.Helpers.uniqid=function(a,b){void 0===a&&(a="");var c,d=function(a,b){return a=parseInt(a,10).toString(16),b<a.length?a.slice(a.length-b):b>a.length?Array(b-a.length+1).join("0")+a:a};return this.php_js||(this.php_js={}),this.php_js.uniqidSeed||(this.php_js.uniqidSeed=Math.floor(123456789*Math.random())),this.php_js.uniqidSeed++,c=a,c+=d(parseInt((new Date).getTime()/1e3,10),8),c+=d(this.php_js.uniqidSeed,5),b&&(c+=(10*Math.random()).toFixed(8).toString()),c},jQuery(function(a){if("undefined"!=typeof pagenow&&"wcct_countdown"===pagenow){a("#wcct_settings_location").change(function(){"custom:custom"==a(this).val()?a(".wcct-settings-custom").show():a(".wcct-settings-custom").hide()}),a("#wcct_settings_location").trigger("change");var b=function(){a(".wcct-date-picker-field").datepicker({dateFormat:"yy-mm-dd",numberOfMonths:1,showButtonPanel:!0}),a("select.chosen_select").xlChosen(),a("select.ajax_chosen_select_products").xlAjaxChosen({method:"GET",url:WCCTParams.ajax_url,dataType:"json",afterTypeDelay:100,data:{action:"woocommerce_json_search_products",security:WCCTParams.search_products_nonce}},function(b){var c={};return a.each(b,function(a,b){c[a]=b}),c}),a("select.ajax_chosen_select").each(function(b){a(b).xlAjaxChosen({method:"GET",url:WCCTParams.ajax_url,dataType:"json",afterTypeDelay:100,data:{action:"wcct_json_search",method:a(b).data("method"),security:WCCTParams.ajax_chosen}},function(b){var c={};return a.each(b,function(a,b){c[a]=b}),c})})};b(),a("#wcct-rules-groups").on("change","select.rule_type",function(){var c=a(this).closest("tr"),d=c.data("ruleid"),e=c.closest("table").data("groupid"),f={action:"wcct_change_rule_type",security:WCCTParams.ajax_nonce,group_id:e,rule_id:d,rule_type:a(this).val()};c.find("td.condition").html("").remove(),c.find("td.operator").html("").remove(),c.find("td.loading").show(),c.find("td.rule-type select").prop("disabled",!0),a.ajax({url:ajaxurl,data:f,type:"post",dataType:"html",success:function(a){c.find("td.loading").hide().before(a),c.find("td.rule-type select").prop("disabled",!1),b()}})});var c=Backbone.View.extend({groupCount:0,el:"#wcct-rules-groups",events:{"click .wcct-add-rule-group":"addRuleGroup"},render:function(){this.$target=this.$(".wcct-rule-group-target"),WCCT.Events.bind("wcct:remove-rule-group",this.removeRuleGroup,this),this.views={};var b=this.$("div.wcct-rule-group-container");_.each(b,function(b){this.groupCount++;var c=a(b).data("groupid"),e=new d({el:b,model:new Backbone.Model({groupId:c,groupCount:this.groupCount,headerText:this.groupCount>1?WCCTParams.text_or:WCCTParams.text_apply_when,removeText:WCCTParams.remove_text})});this.views[c]=e,e.bind("wcct:remove-rule-group",this.removeRuleGroup,this)},this),this.groupCount>0&&a(".rules_or").show()},addRuleGroup:function(c){c.preventDefault();var e="group"+WCCT.Helpers.uniqid();this.groupCount++;var f=new d({model:new Backbone.Model({groupId:e,groupCount:this.groupCount,headerText:this.groupCount>1?WCCTParams.text_or:WCCTParams.text_apply_when,removeText:WCCTParams.remove_text})});return this.$target.append(f.render().el),this.views[e]=f,f.bind("wcct:remove-rule-group",this.removeRuleGroup,this),this.groupCount>0&&a(".rules_or").show(),b(),!1},removeRuleGroup:function(a){delete this.views[a.model.get("groupId")],a.remove()}}),d=Backbone.View.extend({tagName:"div",className:"wcct-rule-group-container",template:_.template('<div class="wcct-rule-group-header"><h4><%= headerText %></h4><a href="#" class="wcct-remove-rule-group button"><%= removeText %></a></div><table class="wcct-rules" data-groupid="<%= groupId %>"><tbody></tbody></table>'),events:{"click .wcct-remove-rule-group":"onRemoveGroupClick"},initialize:function(){this.views={},this.$rows=this.$el.find("table.wcct-rules tbody");var b=this.$("tr.wcct-rule");_.each(b,function(b){var c=a(b).data("ruleid"),d=new e({el:b,model:new Backbone.Model({groupId:this.model.get("groupId"),ruleId:c})});d.delegateEvents(),d.bind("wcct:add-rule",this.onAddRule,this),d.bind("wcct:remove-rule",this.onRemoveRule,this),this.views.ruleId=d},this)},render:function(){return this.$el.html(this.template(this.model.toJSON())),this.$rows=this.$el.find("table.wcct-rules tbody"),this.$el.attr("data-groupid",this.model.get("groupId")),this.onAddRule(null),this},onAddRule:function(a){var c="rule"+WCCT.Helpers.uniqid(),d=new e({model:new Backbone.Model({groupId:this.model.get("groupId"),ruleId:c})});null==a?this.$rows.append(d.render().el):a.$el.after(d.render().el),d.bind("wcct:add-rule",this.onAddRule,this),d.bind("wcct:remove-rule",this.onRemoveRule,this),b(),this.views.ruleId=d},onRemoveRule:function(b){var c=b.model.get("ruleId");1!=a("#wcct-rules-groups table tr.wcct-rule").length&&(delete this.views[c],b.remove(),""==a("table[data-groupid='"+this.model.get("groupId")+"'] tbody").html()&&(WCCT.Events.trigger("wcct:removing-rule-group",this),this.trigger("wcct:remove-rule-group",this)))},onRemoveGroupClick:function(a){return a.preventDefault(),WCCT.Events.trigger("wcct:removing-rule-group",this),this.trigger("wcct:remove-rule-group",this),!1}}),e=Backbone.View.extend({tagName:"tr",className:"wcct-rule",template:_.template(a("#wcct-rule-template").html()),events:{"click .wcct-add-rule":"onAddClick","click .wcct-remove-rule":"onRemoveClick"},render:function(){return this.$el.html(this.template(this.model.toJSON())),this.$el.attr("data-ruleid",this.model.get("ruleId")),this},onAddClick:function(a){return a.preventDefault(),WCCT.Events.trigger("wcct:adding-rule",this),this.trigger("wcct:add-rule",this),!1},onRemoveClick:function(a){return a.preventDefault(),WCCT.Events.trigger("wcct:removing-rule",this),this.trigger("wcct:remove-rule",this),!1}});(new c).render()}});