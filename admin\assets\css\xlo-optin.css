#xlo-wrap {
    width: 480px;
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    margin: 30px 0;
    max-width: 100%
}

#xlo-wrap .xlo-content {
    background: #fff;
    padding: 0 20px 15px
}

#xlo-wrap .xlo-content p {
    margin: 0 0 1em;
    padding: 0;
    font-size: 1.1em
}

#xlo-wrap .xlo-actions {
    padding: 10px 20px;
    background: #C0C7CA;
    position: relative
}

#xlo-wrap .xlo-actions .xlo_loader {
    width: 30px;
    height: 30px;
    border: 2px solid #FFF;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    /* box-sizing: border-box; */
    animation: rotation 1s linear infinite;
    position: absolute;
    left: 37%;
    top: 23%;
    transform: translate(-50%, -50%);
}

#xlo-wrap .xlo-actions .button {
    padding: 0 10px 1px;
    line-height: 35px;
    height: 37px;
    font-size: 16px;
    margin-bottom: 0
}

#xlo-wrap .xlo-actions .button .dashicons {
    font-size: 37px;
    margin-left: -8px;
    margin-right: 12px
}

#xlo-wrap .xlo-actions .button.button-primary {
    padding-right: 15px;
    padding-left: 15px
}

#xlo-wrap .xlo-actions .button.button-primary:after {
    content: ' \279C'
}

#xlo-wrap .xlo-actions .button.button-primary {
    float: right
}

#xlo-wrap.xlo-anonymous-disabled .xlo-actions .button.button-primary {
    width: 100%
}

#xlo-wrap .xlo-error-boundary {
    display: none;
    padding: 8px 5px;
    text-align: center;
}

#xlo-wrap .xlo-permissions {
    padding: 10px 20px;
    background: #FEFEFE;
    -moz-transition: background .5s ease;
    -o-transition: background .5s ease;
    -ms-transition: background .5s ease;
    -webkit-transition: background .5s ease;
    transition: background .5s ease
}

#xlo-wrap .xlo-permissions .xlo-trigger {
    font-size: .9em;
    text-decoration: none;
    text-align: center;
    display: block
}

#xlo-wrap .xlo-permissions ul {
    height: 0;
    overflow: hidden;
    margin: 0
}

#xlo-wrap .xlo-permissions ul li {
    margin-bottom: 12px
}

#xlo-wrap .xlo-permissions ul li:last-child {
    margin-bottom: 0
}

#xlo-wrap .xlo-permissions ul li i.dashicons {
    float: left;
    font-size: 40px;
    width: 40px;
    height: 40px
}

#xlo-wrap .xlo-permissions ul li div {
    margin-left: 55px
}

#xlo-wrap .xlo-permissions ul li div span {
    font-weight: 700;
    text-transform: uppercase;
    color: #23282d
}

#xlo-wrap .xlo-permissions ul li div p {
    margin: 2px 0 0
}

#xlo-wrap .xlo-permissions.xlo-open {
    background: #fff
}

#xlo-wrap .xlo-permissions.xlo-open ul {
    height: auto;
    margin: 20px 20px 10px
}

#xlo-wrap .xlo-logos {
    padding: 20px;
    line-height: 0;
    background: #fafafa;
    height: 84px;
    position: relative
}

#xlo-wrap .xlo-logos .xlo-wrap-logo {
    position: absolute;
    left: 58%;
    top: 20px
}

#xlo-wrap .xlo-logos .xlo-plugin-icon {
    position: absolute;
    top: 20px;
    left: 30%;
    margin-left: -40px
}

#xlo-wrap .xlo-logos .xlo-plugin-icon, #xlo-wrap .xlo-logos img, #xlo-wrap .xlo-logos object {
    width: 80px;
    height: 80px
}

#xlo-wrap .xlo-logos .dashicons-plus {
    position: absolute;
    top: 50%;
    font-size: 30px;
    margin-top: -10px;
    color: #bbb
}

#xlo-wrap .xlo-logos .dashicons-plus.xlo-first {
    left: 45%
}

#xlo-wrap .xlo-logos .xlo-plugin-icon, #xlo-wrap .xlo-logos .xlo-wrap-logo {
    border: 1px solid #ccc;
    padding: 1px;
    background: #fff
}

#xlo-wrap .xlo-terms {
    text-align: center;
    font-size: .85em;
    padding: 5px;
    background: rgba(0, 0, 0, 0.05)
}

#xlo-wrap .xlo-terms, #xlo-wrap .xlo-terms a {
    color: #999
}

#xlo-wrap .xlo-terms a {
    text-decoration: none
}

#xlo-theme_connect_wrapper #xlo-wrap {
    top: 0;
    text-align: left;
    display: inline-block;
    vertical-align: middle;
    margin-top: 52px;
    margin-bottom: 20px
}

#xlo-theme_connect_wrapper #xlo-wrap .xlo-terms {
    background: rgba(140, 140, 140, 0.64)
}

#xlo-theme_connect_wrapper #xlo-wrap .xlo-terms, #xlo-theme_connect_wrapper #xlo-wrap .xlo-terms a {
    color: #c5c5c5
}
