var wcct_timeOut=!1,wcctAllUniqueTimers=[];!function(a){"use strict";function b(){wcct_data.hasOwnProperty("refresh_timings")&&"no"!==wcct_data.refresh_timings&&a(".wcct_countdown_timer .wcct_timer_wrap").each(function(){var b=a(this).parents(".wcct_countdown_timer").attr("data-campaign-id");wcctAllUniqueTimers.indexOf(b)>-1||(wcctAllUniqueTimers.push(b),d(),a.ajax({url:wcct_data.admin_ajax,type:"GET",dataType:"json",data:{wcct_action:"wcct_refreshed_times",location:document.location.href,endDate:a(this).attr("data-date"),campID:a(this).parents(".wcct_countdown_timer").attr("data-campaign-id")},beforeSend:function(){},success:function(b){a(".wcct_countdown_timer[data-campaign-id='"+b.id+"']").each(function(){var a=jQuery(this).children(".wcct_timer_wrap").attr("data-left");if(0===b.diff)switch(jQuery(this).attr("data-type")){case"counter_bar":jQuery(this).parents(".wcct_counter_bar").eq(0).fadeOut().remove();break;case"single":jQuery(this).eq(0).fadeOut().remove()}else{var c=jQuery(this).attr("data-delay");void 0!==c&&b.diff>parseInt(c)&&jQuery(this).remove(),parseInt(a)-parseInt(b.diff)>10&&(jQuery(this).removeAttr("data-wctimer-load"),jQuery(this).children(".wcct_timer_wrap").attr("data-left",b.diff))}}),d()}}))})}function c(){a("#wp-admin-bar-wcct_admin_page_node-default").length>0&&a("#wp-admin-bar-wcct_admin_page_node-default").html(a(".wcct_header_passed").html())}function d(b){void 0===b&&(b=!0),a(".wcct_timer").length>0&&a(".wcct_timer").each(function(){var c=a(this);if("yes"==c.attr("data-wctimer-load"))return!0;var d,e,f,g,h,i,j,k,l,m,n,o=c.find(".wcct_timer_wrap"),p=(parseInt(o.attr("data-date")),o.attr("data-timer-skin")),q=""!=a(this).attr("data-days")?a("<div>").text(a(this).attr("data-days")).html():"day",r=""!=a(this).attr("data-hrs")?a("<div>").text(a(this).attr("data-hrs")).html():"hr",s=""!=a(this).attr("data-mins")?a("<div>").text(a(this).attr("data-mins")).html():"min",t=""!=a(this).attr("data-secs")?a("<div>").text(a(this).attr("data-secs")).html():"sec",u=""!=a(this).attr("data-is_days")?a("<div>").text(a(this).attr("data-is_days")).html():"yes",v=""!=a(this).attr("data-is-hrs")?a("<div>").text(a(this).attr("data-is-hrs")).html():"yes",w=(new Date).getTime()+1e3*parseInt(o.attr("data-left"));o.wcctCountdown(w,{elapse:!0}).on("update.countdown",function(o){if(e=o.offset.seconds,f=o.offset.minutes,g=o.offset.hours,h=i=j=k=l=m=n="","0"==e&&(h=" wcct_pulse wcct_animated",l=" wcct_border_none"),"0"==e&&"0"==h&&(i=" wcct_pulse wcct_animated",m=" wcct_border_none"),"0"==e&&"0"==h&&"0"==i&&(j=" wcct_pulse wcct_animated",n=" wcct_border_none"),d="",o.elapsed&&1==b){var w=c.parents(".wcct_header_area");w.length>0&&(w.find(".wcct_close").trigger("click"),setTimeout(function(){w.remove()},1e3));var x=c.parents(".wcct_footer_area");if(x.length>0&&(x.find(".wcct_close").trigger("click"),setTimeout(function(){x.remove()},1e3)),setTimeout(function(){c.remove()},1e3),!1===wcct_timeOut&&(a.ajax({url:wcct_data.admin_ajax,type:"POST",dataType:"json",data:{action:"wcct_clear_cache"},success:function(a){},timeout:10}),"yes"==wcct_data.reload_page_on_timer_ends)){setTimeout(function(){window.location.reload()},2e3)}}else{var y="%D",z="%H",A="%M",B="%S";0==b&&(y="00",z="00",A="00",B="00"),"round_fill"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_round_wrap '+n+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+'</div></div><div class="wcct_wrap_border '+j+'"></div></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_round_wrap '+m+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+'</div></div><div class="wcct_wrap_border '+i+'"></div></div>'),d+='<div class="wcct_round_wrap '+l+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div><div class="wcct_wrap_border '+h+'"></div></div><div class="wcct_round_wrap wcct_border_none"><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+'</div></div><div class="wcct_wrap_border wcct_pulse wcct_animated"></div></div>'):"round_ghost"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_round_wrap '+n+'"><div class="wcct_wrap_border '+j+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+"</div></div></div>"),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_round_wrap '+m+'"><div class="wcct_wrap_border '+i+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+"</div></div></div>"),d+='<div class="wcct_round_wrap '+l+'"><div class="wcct_wrap_border '+h+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div></div><div class="wcct_round_wrap wcct_border_none"><div class="wcct_wrap_border wcct_pulse wcct_animated"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+"</div></div></div>"):"square_fill"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_square_wrap '+n+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+'</div></div><div class="wcct_wrap_border '+j+'"></div></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_square_wrap '+m+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+'</div></div><div class="wcct_wrap_border '+i+'"></div></div>'),d+='<div class="wcct_square_wrap '+l+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div><div class="wcct_wrap_border '+h+'"></div></div><div class="wcct_square_wrap wcct_border_none"><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+'</div></div><div class="wcct_wrap_border wcct_pulse wcct_animated"></div></div>'):"square_ghost"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_square_wrap '+n+'"><div class="wcct_wrap_border '+j+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+"</div></div></div>"),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_square_wrap '+m+'"><div class="wcct_wrap_border '+i+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+"</div></div></div>"),d+='<div class="wcct_square_wrap '+l+'"><div class="wcct_wrap_border '+h+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div></div><div class="wcct_square_wrap wcct_border_none"><div class="wcct_wrap_border wcct_pulse wcct_animated"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+"</div></div></div>"):"highlight_1"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+y+"</span> "+q+'<span class="wcct_colon_sep">:</span></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+z+"</span> "+r+'<span class="wcct_colon_sep">:</span></div>'),d+='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+A+"</span> "+s+'<span class="wcct_colon_sep">:</span></div><div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+B+"</span> "+t+"</div>"):((o.offset.totalDays>0||"yes"==u)&&(d=y+q),(o.offset.totalHours>0||"yes"==v)&&(d+=" "+z+r),d+=" "+A+s+" "+B+t),a(this).html(o.strftime(d))}}),c.attr("data-wctimer-load","yes")})}function e(){a(".wcct_counter_bar").length>0&&a(".wcct_counter_bar").each(function(){var b=a(this);if(b.css("display","block"),b.find(".wcct_progress_aria").length>0){var c=b.find(".wcct_progress_aria");if(c.visible(!0)&&!c.hasClass("wcct_bar_active")){c.addClass("wcct_bar_active");var d=c.find(".wcct_progress_bar").attr("aria-valuenow");setTimeout(function(){c.find(".wcct_progress_bar").css("width",d+"%")},200)}}})}a(".variations_form").on("woocommerce_variation_select_change",function(){}),a(".variations_form").on("show_variation",function(a,b){}),a(document).ready(function(){b(),d(),e(),c()}),a(window).scroll(function(){e()}),a(document).bind("wc_fragments_refreshed",function(){d()})}(jQuery);