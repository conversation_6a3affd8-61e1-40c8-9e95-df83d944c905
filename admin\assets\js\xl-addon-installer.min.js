jQuery(document).ready(function(a){a("#xli_product_image").click(function(a){a.preventDefault()}),a("#xl_install_button").on("click",function(){var b=a(this),c=b.data("xl-slug"),d=b.data("xl-file"),e=a("#xli_step_one");e.css("opacity","1");var f=a("#xli_activated_img"),g=a("#xli_step_two"),h=a("#step-completed"),i=a("#step2-completed"),j=a("#xli_setup"),k=a("#xl_setup_link");b.prop("disabled",!0),e.show(),a.ajax({url:xl_installer_data.ajaxUrl,type:"POST",data:{action:"xl_addon_installation",xl_slug:c,xl_file:d,nonce:xl_installer_data.nonce},success:function(c){if(console.log(c),b.prop("disabled",!0),c&&!1!==c.success){e.css("opacity","0"),f.css("display","none"),g.css("display","none"),h.css("display","block"),i.css("display","block"),k.css("pointer-events","all"),j.css("opacity","1");var d=a("<div>"+c+"</div>"),l=(d.find("p"),a('<div><div class="xl_installation_alert_success">Plugin installed and activated successfully!</div></div>'));a("#xl_installation_alert_success").prepend(l)}else{b.prop("disabled",!1),e.css("opacity","0");var m=c&&c.data?c.data:"Something went wrong. Please try again.",n=a('<div><div class="xl_installation_alert">'+m+"</div></div>");a("#xl_installation_error").prepend(n)}},error:function(c,d,f){b.prop("disabled",!1),e.css("opacity","0"),console.error(c.responseText);var g=a('<div class="notice notice-error is-dismissible"><p>Error: '+c.responseText+"</p></div>");a("#wpbody-content").prepend(g)}})});var b=a("#xli_image_modal"),c=a("#xli_product_image"),d=a("#img01");c.on("click",function(){b.css("display","block"),d.attr("src",a(this).attr("src")),b.addClass("xli-zoom-in")}),a(".xli_close_btn").eq(0).on("click",function(){b.css("display","none")})});