/*!
 * jquery-confirm v2.0.0 (http://craftpip.github.io/jquery-confirm/)
 * Author: <PERSON><PERSON><PERSON>
 * Website: www.craftpip.com
 * Contact: <EMAIL>
 *
 * Copyright 2013-2015 jquery-confirm
 * Licensed under MIT (https://github.com/craftpip/jquery-confirm/blob/master/LICENSE)
 */
if (typeof jQuery === "undefined"){throw new Error("jquery-confirm requires jQuery")}var jconfirm, Jconfirm; (function(b){b.fn.xlConfirm = function(c){if (typeof c === "undefined"){c = {}}var d = b(this); d.on("click", function(f){f.preventDefault(); if (d.attr("href")){c.xlConfirm = function(){location.href = d.attr("href")}}b.xlConfirm(c)}); return d}; b.xlConfirm = function(c){return jconfirm(c)}; b.xlAlert = function(c){c.cancelButton = false; return jconfirm(c)}; b.xlDialog = function(c){c.cancelButton = false; c.confirmButton = false; c.confirmKeys = [13]; return jconfirm(c)}; jconfirm = function(c){if (typeof c === "undefined"){c = {}}if (jconfirm.defaults){b.extend(jconfirm.pluginDefaults, jconfirm.defaults)}var c = b.extend({}, jconfirm.pluginDefaults, c); return new Jconfirm(c)}; Jconfirm = function(c){b.extend(this, c); this._init()}; Jconfirm.prototype = {_init:function(){var c = this; this._rand = Math.round(Math.random() * 99999); this._buildHTML(); this._bindEvents(); setTimeout(function(){c.open()}, 0)}, animations:["anim-scale", "anim-top", "anim-bottom", "anim-left", "anim-right", "anim-zoom", "anim-opacity", "anim-none", "anim-rotate", "anim-rotatex", "anim-rotatey", "anim-scalex", "anim-scaley"], _buildHTML:function(){this.animation = "anim-" + this.animation.toLowerCase(); this.closeAnimation = "anim-" + this.closeAnimation.toLowerCase(); this.$el = b(this.template).appendTo(this.container).addClass(this.theme); this.$el.find(".jconfirm-box-container").addClass(this.columnClass); this.CSS = {"-webkit-transition-duration":this.animationSpeed / 1000 + "s", "transition-duration":this.animationSpeed / 1000 + "s", "-webkit-transition-timing-function":"cubic-bezier(.38,1.28,.2, " + this.animationBounce + ")", "transition-timing-function":"cubic-bezier(.38,1.28,.2, " + this.animationBounce + ")"}; this.$el.find(".jconfirm-bg").css(this.CSS); this.$b = this.$el.find(".jconfirm-box").css(this.CSS).addClass(this.animation); this.$body = this.$b; if (this.rtl){this.$el.addClass("rtl")}this.$title = this.$el.find("div.title"); this.setTitle(); this.contentDiv = this.$el.find("div.content"); this.$content = this.contentDiv; this.$btnc = this.$el.find(".buttons"); if (this.confirmButton && this.confirmButton.trim() !== ""){this.$confirmButton = b('<button class="btn">' + this.confirmButton + "</button>").appendTo(this.$btnc).addClass(this.confirmButtonClass)}if (this.cancelButton && this.cancelButton.trim() !== ""){this.$cancelButton = b('<button class="btn">' + this.cancelButton + "</button>").appendTo(this.$btnc).addClass(this.cancelButtonClass)}if (!this.confirmButton && !this.cancelButton){this.$btnc.remove()}if (!this.confirmButton && !this.cancelButton && this.closeIcon === null){this.$closeButton = this.$b.find(".closeIcon").show()}if (this.closeIcon === true){this.$closeButton = this.$b.find(".closeIcon").show()}this.setContent(); if (this.autoClose){this._startCountDown()}}, setTitle:function(c){this.title = (typeof c !== "undefined")?c:this.title; if (this.title && this.$title){this.$title.html('<i class="' + this.icon + '"></i> ' + this.title)} else{this.$title.remove()}}, setContent:function(e){var f = this; this.content = (e)?e:this.content; var c = (e)?true:false; if (typeof this.content === "boolean"){if (!this.content){this.contentDiv.remove()} else{console.error("Invalid option for property content: passed TRUE")}} else{if (typeof this.content === "string"){if (this.content.substr(0, 4).toLowerCase() === "url:"){this.contentDiv.html(""); this.$btnc.find("button").prop("disabled", true); var d = this.content.substring(4, this.content.length); b.get(d).done(function(h){f.contentDiv.html(h)}).always(function(i, h, j){if (typeof f.contentLoaded === "function"){f.contentLoaded(i, h, j)}f.$btnc.find("button").prop("disabled", false); f.setDialogCenter()})} else{this.contentDiv.html(this.content)}} else{if (typeof this.content === "function"){this.contentDiv.html(""); this.$btnc.find("button").attr("disabled", "disabled"); var g = this.content(this); if (typeof g !== "object"){console.error("The content function must return jquery promise.")} else{if (typeof g.always !== "function"){console.error("The object returned is not a jquery promise.")} else{g.always(function(i, h){f.$btnc.find("button").removeAttr("disabled"); f.setDialogCenter()})}}} else{console.error("Invalid option for property content, passed: " + typeof this.content)}}}this.setDialogCenter(c)}, _startCountDown:function(){var c = this.autoClose.split("|"); if (/cancel/.test(c[0]) && this.type === "alert"){return false} else{if (/confirm|cancel/.test(c[0])){this.$cd = b('<span class="countdown">').appendTo(this["$" + c[0] + "Button"]); var d = this; d.$cd.parent().click(); var e = c[1] / 1000; this.interval = setInterval(function(){d.$cd.html(" [" + (e -= 1) + "]"); if (e === 0){d.$cd.parent().trigger("click"); clearInterval(d.interval)}}, 1000)} else{console.error("Invalid option " + c[0] + ", must be confirm/cancel")}}}, _bindEvents:function(){var d = this; var c = false; this.$el.find(".jconfirm-scrollpane").click(function(f){if (!c){if (d.backgroundDismiss){d.cancel(); d.close()} else{d.$b.addClass("hilight"); setTimeout(function(){d.$b.removeClass("hilight")}, 400)}}c = false}); this.$el.find(".jconfirm-box").click(function(f){c = true}); if (this.$confirmButton){this.$confirmButton.click(function(g){g.preventDefault(); var f = d.confirm(d.$b); d.onAction("confirm"); if (typeof f === "undefined" || f){d.close()}})}if (this.$cancelButton){this.$cancelButton.click(function(g){g.preventDefault(); var f = d.cancel(d.$b); d.onAction("cancel"); if (typeof f === "undefined" || f){d.close()}})}if (this.$closeButton){this.$closeButton.click(function(f){f.preventDefault(); d.cancel(); d.onAction("close"); d.close()})}if (this.keyboardEnabled){setTimeout(function(){b(window).on("keyup." + this._rand, function(f){d.reactOnKey(f)})}, 500)}b(window).on("resize." + this._rand, function(){d.setDialogCenter(true)})}, reactOnKey:function a(f){var c = b(".jconfirm"); if (c.eq(c.length - 1)[0] !== this.$el[0]){return false}var d = f.which; if (this.contentDiv.find(":input").is(":focus") && /13|32/.test(d)){return false}if (b.inArray(d, this.cancelKeys) !== - 1){if (!this.backgroundDismiss){this.$el.find(".jconfirm-bg").click(); return false}if (this.$cancelButton){this.$cancelButton.click()} else{this.close()}}if (b.inArray(d, this.confirmKeys) !== - 1){if (this.$confirmButton){this.$confirmButton.click()}}}, setDialogCenter:function(d){var h = b(window).height(); var g = this.$b.outerHeight(); var c = (h - g) / 2; var f = 100; if (g > (h - f)){var e = {"margin-top":f / 2, "margin-bottom":f / 2}} else{var e = {"margin-top":c}}if (d){this.$b.animate(e, {duration:this.animationSpeed, queue:false})} else{this.$b.css(e)}}, close:function(){var c = this; if (this.isClosed()){return false}if (typeof this.onClose === "function"){this.onClose()}b(window).unbind("resize." + this._rand); if (this.keyboardEnabled){b(window).unbind("keyup." + this._rand)}c.$el.find(".jconfirm-bg").removeClass("seen"); this.$b.addClass(this.closeAnimation); var d = (this.closeAnimation == "anim-none")?0:this.animationSpeed; setTimeout(function(){c.$el.remove()}, d + 50); jconfirm.record.closed += 1; jconfirm.record.currentlyOpen -= 1; if (jconfirm.record.currentlyOpen < 1){b("body").removeClass("jconfirm-noscroll")}return true}, open:function(){var d = this; if (this.isClosed()){return false}d.$el.find(".jconfirm-bg").addClass("seen"); b("body").addClass("jconfirm-noscroll"); this.$b.removeClass(this.animations.join(" ")); this.$b.find("input[autofocus]:visible:first").focus(); jconfirm.record.opened += 1; jconfirm.record.currentlyOpen += 1; if (typeof this.onOpen === "function"){this.onOpen()}var c = "jconfirm-box" + this._rand; this.$b.attr("aria-labelledby", c).attr("tabindex", - 1).focus(); if (this.$title){this.$title.attr("id", c)} else{if (this.$content){this.$content.attr("id", c)}}return true}, isClosed:function(){return this.$el.css("display") === ""}}; jconfirm.pluginDefaults = {template:'<div class="jconfirm"><div class="jconfirm-bg"></div><div class="jconfirm-scrollpane"><div class="container"><div class="row"><div class="jconfirm-box-container span6 offset3"><div class="jconfirm-box" role="dialog" aria-labelledby="labelled" tabindex="-1"><div class="closeIcon"><span class="glyphicon glyphicon-remove"></span></div><div class="title"></div><div class="content"></div><div class="buttons"></div><div class="jquery-clear"></div></div></div></div></div></div></div>', title:"Hello", content:"Are you sure to continue?", contentLoaded:function(){}, icon:"", confirmButton:"Okay", cancelButton:"Cancel", confirmButtonClass:"btn-default", cancelButtonClass:"btn-default", theme:"white", animation:"zoom", closeAnimation:"scale", animationSpeed:500, animationBounce:1.2, keyboardEnabled:false, rtl:false, confirmKeys:[13, 32], cancelKeys:[27], container:"body", xlConfirm:function(){}, cancel:function(){}, backgroundDismiss:true, autoClose:false, closeIcon:null, columnClass:"col-md-4 col-md-offset-4", onOpen:function(){}, onClose:function(){}, onAction:function(){}}; jconfirm.record = {opened:0, closed:0, currentlyOpen:0}})(jQuery);