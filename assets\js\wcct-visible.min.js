!function(a){var b=a(window);a.fn.visible=function(a,c,d){if(!(this.length<1)){var e=this.length>1?this.eq(0):this,f=e.get(0),g=b.width(),h=b.height(),d=d||"both",i=!0!==c||f.offsetWidth*f.offsetHeight;if("function"==typeof f.getBoundingClientRect){var j=f.getBoundingClientRect(),k=j.top>=0&&j.top<h,l=j.bottom>0&&j.bottom<=h,m=j.left>=0&&j.left<g,n=j.right>0&&j.right<=g,o=a?k||l:k&&l,p=a?m||n:m&&n;if("both"===d)return i&&o&&p;if("vertical"===d)return i&&o;if("horizontal"===d)return i&&p}else{var q=b.scrollTop(),r=q+h,s=b.scrollLeft(),t=s+g,u=e.offset(),v=u.top,w=v+e.height(),x=u.left,y=x+e.width(),z=!0===a?w:v,A=!0===a?v:w,B=!0===a?y:x,C=!0===a?x:y;if("both"===d)return!!i&&r>=A&&z>=q&&t>=C&&B>=s;if("vertical"===d)return!!i&&r>=A&&z>=q;if("horizontal"===d)return!!i&&t>=C&&B>=s}}}}(jQuery);