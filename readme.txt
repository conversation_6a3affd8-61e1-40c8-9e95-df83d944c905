=== Finale Lite - Sales Countdown Timer & Discount for WooCommerce ===
Contributors: xlplugins
Tags: W<PERSON>Commerce, WooCommerce Sales Countdown, WooCommerce Countdown Timer, WooCommerce Bulk Discount, WooCommerce Recurring Campaigns, WooCommerce Sales Scheduler, WooCommerce Pre Sale, WooCommerce Counter Bar, XLPlugins, eCommerce, WooCommerce Promotions, WooCommerce Deals, WooCommerce Discounts, WooCommerc Countdown Timer, WooCommerce Scheduled Sales
Requires at least: 5.0
Tested up to: 6.7.2
Stable tag: 2.20.0
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Finale lets you create scheduled one time or recurring campaigns. It induces urgency with visual elements such as Countdown Timer and Counter Bar to motivate users to place an order.

== Description ==

[Finale](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Finale) is the ONLY WooCommerce plugin that allows you to create urgency and scarcity inducing promotional campaigns.
Urgency and scarcity are both powerful psychological triggers that motivate shoppers to take fast action and avoid missing out.

You can use Finale to run scheduled sales campaigns such as flash sales, seasonal promotions, coupon code led discounts, pre-launch offers, daily deals, faster shipping deadline campaigns, instant discounts, early bird deals, recurring offers and more.

> [Explore full capabilities of Finale in this article about WooCommerce Discounts](https://xlplugins.com/woocommerce-discounts-deals/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=WooCommerce-discounts-deals)

Just plug in the promotion details and get started in minutes. Here's how:

https://www.youtube.com/watch?v=7zQnWMGF6rY&t=6s

Finale is built to empower store owners who want to run promotions and convert more visitors into buyers. It's for those who don't believe in 'let's hope they'll buy someday' philosophy. Instead want to seize every opportunity to generate more orders.

> "This plugin is everything I was looking for and more. The best part is that all the settings are intuitive- I didn't have to spend hours figuring things out. I could get started within minutes of installation. And yes, a big thumbs up for support, they ensure you win with the plugin."
> -Phil de Gruchy Founder, Blue Lama

== Finale's Lite Features ==

1. **[Set Up Sales Between Two Fixed Dates and Time](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
In WooCommerce you cannot start a campaign at a set time. But Finale allows you to pick a time. This is great for running flash sales like happy hours, deals of the day (Wow Wednesdays, Tipsy Thursdays), prime time specials, lunch hour deals and more.


2. **[Run a Countdown Timer to Give Shoppers a Clear Deadline for Action](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Most shoppers wait until the last few hours to make their purchase. In fact a typical sales graph shows that 90% of sales happen on the big last day. It's when the fear of missing out is at its peak. Make this insight into buyer psyche work in your favor.
Use Finale to set up stunning countdown timers on the product page to remind shoppers about the deadline. You'll be surprised by the power of a subtle reminder.

3.  **[Set up a counter bar to show the real-time stock status ](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Visualization is powerful. Don't tell your visitors the number of items left in stock, show them. A counter bar shows the real time stock status and gives people a compelling reason to hurry up. It's persuasive and moves as more and more people buy. Similar to Amazon's counter bar on daily deals that says 'x% of deal claimed', it gives you the power to use scarcity with class!

4.  **[Create rules to customize campaigns](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Rule builder allows you to create your rules to customize campaigns. It makes the experience of using Finale hassle-free. No need to visit individual product pages to set up campaigns. Choose from 4 nifty rules to customize your campaigns. Set up campaigns: On certain hand-picked products, or all products in a specified product category, or chosen product type or only on products under or above a certain price point.




== Finale: The Must Have Tool For Running Scarcity & Urgency Led Promotions At Any Time of The Year ==

**Set up your deals of the day, clearance sales, end-of-season offers, early bird discounts, members-only offers, and festive campaigns in minutes.**

> "Finale is a game changer. It's one of the most effective sales-booster I've ever tried! It's amazing how easy is to manage sales and display triggers that motivate customers to purchase more and come back to the store. Different sets of conditions and types of rules lead to impressive results. The outcome is almost unbelievable! Also the support is outstanding... Thanks, Daman you did a great job!"
> - Razvan Popescu

Watch this video to set up your first promotion in 2 minutes.

https://www.youtube.com/watch?v=MAKcGagngtI

No matter what time of the year, people always need a push or a reason to make a decision. Finale gives them that credible reason they need to say yes.

== Use Cases of Finale Lite ==

Here are just a few use cases of Finale

* Set up Flash sales scheduled to start on a set date/time

* Create Seasonal offers scheduled to trigger on set dates

* Run Exclusive Happy Hour Deals that only last for a few hours

* Set up Store  Anniversary/Birthday Specials that last for a day and end at 11:59 pm

* Run Store-wide campaigns or only  setup product category-wide sales

* Run Exclusive Festive deals that last till the stock lasts and end once the inventory goal is met

* Feed your entire promotional calendar in one go

* Show the depleting stock size through a counter bar to sell limited stock products


There's more ground you can cover with [Finale Pro](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)

== Finale PRO's Features ==

In addition to the features in Lite, Finale pro has the following core features:

1. **[Set Up Recurring Campaigns That Stop And Re-Start On Automation](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Do you want to run frequent offers and campaigns without having to go to admin panel to re-start? Choose the start date/time, duration, pause period and number of recurrences.
The campaign will start, pause and re-start as per your inputs. Never again log back in to re-start campaigns.

2. **[Set up High-Converting Sticky Header & Footer](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Announce new campaigns in style. Put up urgency-triggering elements like the sticky header/footer on your store. Introduce new arrivals, announce campaigns, category-wide or store-wide discounts, time-limited offers with class. Increase click-through rate to the relevant product pages/ category pages with offers.

3. **[Display Countdown Timer In Your Email Campaigns & More](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Grab the shortCodes and display these visually compelling countdown timers or counter bars on dedicated landing pages, blog posts, pop-ups and more.

4. **[Generate buzz for upcoming products](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Lift curtains off new products in style. Set up a countdown timer on upcoming products and hide the ‘Add to Cart' button during the waiting period. Set it to become auto-visible as soon as the counter strikes zero.

5. **[Maximize conversions through Smart Events](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Instruct Finale to execute decisions based on real-time status. Bump up discounts when the campaign expiry time is close. Or increase the prices of products when remaining stock is little. Or bump up total units when few items are left in stock. You can even use Events to offer Early Bird Discounts (limit discounts to the first set of buyers).

6. **[Embed Shortcodes](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
Finale has 3 short codes for your campaigns (countdown timer, counter bar and custom text). Grab these short codes and embed them in your site and also in emails. Think dedicated landing pages, blog posts, urgency-inducing emails etc. Great for flash sales, festive campaigns, new launches and more.

7. **[Finale Deal Pages](https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main)**
List all your deals on a single page so that shoppers can discover and browse them easily. They'll no longer have to scout for good deals and offers in your store. You can choose from a well-presented grid and list format to display your deals. List all your Christmas sale or Black Friday offers on one page during the promotion season. Also, show countdown timers and scarcity bar on this special curated page.

== Use Cases of Finale PRO ==

Here's a snapshot of a few interesting use cases of Finale Pro. The possibilities are endless.

* Run store-wide recurring flash sales or only on certain products/product category

* Automate your regular weekend offers on chosen products

* Spotlight special campaigns such as Christmas/ Black Friday/Mother's Day etc. through sticky headers/footers

* Use Short codes to embed countdown timers on popups, landing pages, blog posts and sidebars

* Create buzz for upcoming products: Start countdown timers gearing up to the launch date/time

* Run campaigns on products with a specified stock status

* Set up frequent ‘Deals of the Day' campaigns on automation

* Show a certain campaign to people from a chosen country. And another campaign to people from another country

* Personalize your campaigns and show them to logged in members only

* Hide ‘Add to Cart' button on Out of Stock Products

* Trigger Early Bird Discounts on new arrivals that expire after a specified number of units sell

* Increase/Decrease discounts based on the units sold/left

* Set up a countdown timer to nudge shoppers to buy before same/next day shipment deadline expires

== Quick Start Tutorial ==

Watch this ‘Quick Start Tutorial' to hit the ground running with Finale:

https://www.youtube.com/watch?v=rtGqgAzHqrY

> [Frontend Demo: Experience all the visual elements of Finale](http://demo.xlplugins.com/finale/product/demo-of-countdown-timer-counter-bar-sticky-header/)

== Imagine ==

* Having all the tools you need to shed your visitors' inertia and to jolt them into action
* Having the controls to plan and set up your promotional calendars in one go
* Being able to customize your campaigns based on 15 different conditions
* Owning a high-converting store that consistently clocks high sales


= Who should use Finale? =

* Ambitious store owners committed to driving more revenue to their business this season.
* Store owners tired of letting interested shoppers leave their site and postponing their purchase decision
* Store owners who're ready to generate more cash from the same traffic without spending a dime more on ads
* Those who understand the importance of shopper psychology in sales

**If this is you, you're the right fit for Finale.**

= Think About This =

**Would you ever make it to the airport on time if flights didn't have a set departure time?**

No. We speed our car, grab coffee on the way and do whatever it takes to make it on time.

Why? Because there is a definite deadline attached to the task.

We experience fear of missing out and fear of losing money on the tickets. This propels us into fast action.

Similarly deadline-based campaigns and stock scarcity make people experience fear of missing out.

This acts against their natural procrastination and nudges them to the finish line.

== Why We Built Finale? ==

Most store owners run campaigns. But they are ineffective without awareness and definitive expiry deadline.

We [studied several top stores](https://xlplugins.com/woocommerce-discounts-deals/) to see what they were doing to clock high conversion rates.

And discovered they had the master key to sales i.e. shopper psychology nailed.

Here is what Top store owners do to increase conversions:

* Run Time-bound flash sales
* Introduce Limited-edition products
* Announce Exclusive deals of the day and weekend offers
* Build hype around new launches
* Create awareness around festive campaigns through visual elements on the store
* Put up a faster shipment deadline.

These well-planned campaigns help them set high sales records. We wanted to bring the same [bag o' nifty conversion hacks](https://xlplugins.com/woocommerce-discounts-deals/) to small WooCommerce store owners.

And that's what led to Finale.

Also, store owners like you told us they wanted us to build a plugin that could help them run recurring campaigns.

They found it a tedious task to go back to the admin panel to re-start campaigns.

These demands solidified the core features of Finale.

== About The Team ==

Finale is backed and supported by a strong team of developers, support engineers and marketers from <a href="https://xlplugins.com/">XLPlugins</a>. The team is constantly updating the plugin and ensuring its compatibility with the latest WooCommerce versions.

We're also coming up with new and varied use cases of Finale and creating short videos around it.


Our aim is to educate store owners to make the most of our tools.

== Frequently Asked Questions ==

= Can I set up my annual promotional calendar in Finale? =
Yes, make the most of Finale by setting up your entire promotional calendar in one go. Slot Thanksgiving, Hanukkah, Mother's Day, Black Friday etc.

Never miss an opportunity to pull more sales from your store.

= Can a campaign restart on its own without me having to go back to the backend? =
Yes! In <a href="https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main">Finale PRO</a>, you can set up a recurring campaign by selecting the start date/time, pause period and duration. The campaign will start, pause and restart as per your inputs. Your campaigns on autopilot!

= Can I set up the campaign on specific products without visiting separate product pages? =
Yes, that's exactly what the Rule Builder in Finale allows you to do. You don't have to take the hassle of visiting individual product pages. Go to the Rule builder section and set up campaigns based on products, categories, days, time, users, inventory and more.

= Can I run countdown irrespective of sales? =
Yes, you don't need to set sales to trigger a countdown timer. It can work irrespective of sale. Use it to rewards shoppers with same day/next shipping for buying before counter strikes zero. Or to launch new products & create hype!

= Is this plugin regularly updated? =
Yes, an army of developers is working on this project. The plugin is updated based on the feedback we receive from users. We are relentlessly working to ensure Finale becomes an indispensable tool for you.

= What if I don't offer discounts on my store, will Finale still be useful for me? =
Finale goes way beyond discounts. We designed to push fence-sitters on the buying side. Use it to rewards shoppers with same day/next shipping for buying before counter strikes zero. Or to launch new products/product line with a bang! Or to even display limited stock quantity using the counter bar and drum up scarcity.

= Is it possible to show this countdown timer/counter bar anywhere else? =
Yes, in <a href="https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main">Finale PRO</a> you can use short codes to embed countdown timer/counter on your landing pages, blog posts, home page, popups etc

= How do you provide support? =
We provide support through WordPress forums to Finale users. <a href="https://xlplugins.com/finale-woocommerce-sales-countdown-timer-discount-plugin/?utm_source=finale-lite&utm_campaign=wp-repo&utm_medium=readme&utm_term=Main">Finale PRO</a> users can access support through support desk and live chat. Go play, we have your back!



== Installation ==
1. Install "Finale - WooCommerce Sales Countdown Timer & Discount Plugin Lite" Plugin.
2. Activate the Plugin.
3. Go to XlPlugins  ->  Finale Lite
4. Activate Campaign.


== Screenshots ==

1. View All Your Campaigns
2. Schedule Your campaign
3. Configure Discount Settings
4. Configure Inventory Settings
5. Style Your Countdown Timer
6. Style Your Inventory Bar
7. Designs for Countdown Timers
8. Single Product Page Preview

== Change log ==

= 2.20.0 (2025-02-26) =
* Compatible upto WordPress 6.7.2
* Compatible upto WooCommerce 9.7.0
* Security: Wordfence DOM-based cross-site scripting security fix. (#142).
* Improved: Device detection library updated. (#146).
* Dev: Filter hook to remove notices. (#144).


= 2.19.0 (2024-08-29) =
* Compatible upto WordPress 6.6.1
* Compatible upto WooCommerce 9.2.3


= 2.18.2 (2024-04-22) =
* Compatible upto WordPress 6.5.2
* Compatible upto WooCommerce 8.8.2
* Fixed: JS code corrected after optin. (#135)


= 2.18.1 (2024-03-13) =
* Security: Hardened security on the optin box that comes once after installation. (#132)


= 2.18.0 (2024-02-27) =
* Compatible upto WordPress 6.4.3
* Compatible upto WooCommerce 8.6.1
* Improved: Restricting CSS and JS assets on required page only, not on all admin pages. (#126)
* Security: Hardened security on reference plugin installation & download tools code, Fixed. (#125, #128)


= 2.17.0 (2023-12-04) =
* Compatible upto WordPress 6.4.1
* Compatible upto WooCommerce 8.3.1
* PHP 8.1 related fixes. (#108, #119)
* Security: Hardened security for search requests and wp json endpoint of file deletion. (#117)
* Added: Support for WooCommerce HPOS is added. (#110)
* Added: Competition product type is now supported for Finale campaigns. (#112)


= 2.16.0 (2023-01-09) =
* Compatible upto WordPress 6.1.1
* Compatible upto WooCommerce 7.3.0
* Updated: CMB2 plugin dependency updated. (#102)
* Fixed: One notice was coming, fixed. (#104)
* Fixed: Plugin update notice close issue fixed. (#99)


= 2.15.0 (2022-03-29) =
* Compatible upto WordPress 5.9.2
* Compatible upto WooCommerce 6.3.1
* Fixed: A conditional admin setting not working in Firefox, fixed. (#95)


= 2.14.1 (2021-08-14) =
* Compatible upto WordPress 5.8.0
* Compatible upto WooCommerce 5.6.0
* Added: Compatibility added with 'Currency switcher WooCommerce pro' plugin by Algoritmika Ltd. Issue with the pricing detected. (fix/91)
* Improved: Nitro theme compatibility improved. (fix/89)


= 2.14.0 (2021-05-03) =
* Compatible upto WordPress 5.7.1
* Compatible upto WooCommerce 5.3
* Added: Compatibility added with 'Product CSV import export for WC' plugin. (fix/82)
* Added: Compatibility added with WooMulticurrency plugin by TIVNETIC. (fix/84)
* Improved: Lottery product type is now supported for Finale campaigns. (fix/60)
* Fixed: Getting js error on Finale Campaign edit Screen with WP HR manager plugin, fixed. (fix/58)
* Fixed: Updated CMB2 version as getting conflict with Rank Math Seo plugin, fixed. (fix/64)


= 2.13.0 (2020-09-09) =
* Added: Compatible with WordPress 5.5
* Added: Compatible with WooCommerce 4.5
* Added: Compatibility added with 'WooCommerce Conversion Tracking' plugin by weDevs. Same constant and classname was used. (fix/47)
* Added: WP filter hook to allow custom post types for Finale campaign discounting. (fix/34)
* Added: Compatibility added with 'Woocommerce multi currency' plugin by TIVNETINC. (fix/37)
* Fixed: 'Content Tooltip' plugin causing JS error on Finale campaign page, resolved. (fix/53)
* Fixed: 'Product price' rule has some issue with >= operator, fixed. (fix/51)
* Fixed: Campaign with product related rule does not work when product is updated through rest api is resolved. (fix/49)
* Fixed: Campaign with tag in the rule does not work for products that have the TAG added after the campaign has started, fixed. (fix/45)
* Fixed: PHP error in case manage stock is disabled on the product level and custom stock is enable in the Finale campaign. With latest WC version, fixed. (fix/39)


= 2.12.0 (2020-03-31) =
* Added: Compatible with WordPress 5.4
* Added: Compatible with WooCommerce 4.0
* Added: Compatibility added with 'WP Webinar System Pro' plugin, added support of product type 'webinar'. (fix/12)
* Added: Compatibility added with 'WooCommerce field factory' plugin, double discounting was applying. (fix/29)
* Fixed: Admin JS error with 'ThePlus Elementor addon' plugin, resolved. (fix/22)
* Fixed: Product selection in rules is not validating with WPML language, fixed. (fix/26)
* Fixed: Compatibility issues with 'Breeze' plugin, caching not allowing countdown timer to change, fixed. (fix/13)
* Added: Clearing cache by 'WP Fastest Cache' plugin after Finale campaign ends. (fix/34)
* Added: Compatibility added with 'WooCommerce Advanced Bulk Edit' plugin to resolve product category rule issue. (fix/32)
* Added: Turkish localization added (fix/24)
* Fixed: Product rule not fetching the correct product ID with WPML, resolved. (fix/20)


= 2.11.1 (2019-11-15) =
* Added: Compatible with WordPress 5.3
* Added: Compatible with WooCommerce 3.8
* Fixed: Expired Finale campaign meta keys in products are removing properly now.


= 2.11.0 (2019-11-06) =
* Improved: Clear WordPress cache when timer hits zero and page is reloaded.
* Improved: Cron for clearing database of old entries improved.
* Fixed: Incorrect page id fetched for WPML page, resolved now.


= 2.10.0 (2019-07-02) =
* Added: Clearing Autoptimised cache after campaign is finished.
* Added: Compatible with multi currency switcher free & paid plugin by Villatheme.
* Added: Cleaning Finale campaign product meta keys after campaign is finished.
* Improved: Product category rules optimized to speed up the performance.
* Fixed: Incorrect price range display issue resolved on variable products in case, taxes are enabled and entered exclusively.
* Fixed: Clever mega menu plugin's script causing JS conflicts on single Finale campaign page, resolved.
* Fixed: PHP error with OceanWP theme resolved now.


= 2.9.1 (2019-05-30) =
* Improved: Depreciated unused code was running, cleaned now.
* WooCommerce tested upto 3.6.5 and WordPress tested upto 5.2.1.


= 2.9.0 (2019-05-23) =
* Improved: X-Store theme modified their single product code in recent version, that caused positions mismatch, compatibility updated.
* Fixed: Yith Product Bundle Premium plugin has some code which was contradicting with Finale, resolved.
* Fixed: Sold Inventory not updating properly in case of more than 2 quantities purchased, fixed now.


= 2.8.0 (2019-03-28) =
* Added: A new setting is added to reload the page when countdown timer hits zero.
* Fixed: Incorrect prices displayed in the cart with WooCommerce currency switcher plugin, fixed.
* Fixed: Simple product not showing on sale when discount applied by campaign issue fixed.
* Fixed: Swift framework plugin conflict with finale fixed now.
* Fixed: Incorrect inventory displayed when different variations of the same product are purchased in the same order, resolved now.


= 2.7.0 (2019-01-25) =
* Added: Compatible with WooCommerce product addon - By nmedia.
* Added: Compatible with Polylang plugin to support multilingual campaigns.
* Added: Compatible with WP fastest cache plugin, auto cleared cache at certain actions.
* Added: Learndash course product type support added.
* Fixed: JS error due to "massive VC add-on" plugin on edit single campaign page fixed.
* Fixed: Sometimes during execution, regular price was not fetched very early, that caused a warning, fixed now.
* Fixed: CMB2 group field meta boxes don't have the ID, which sometimes causes conflicts with other plugins, fixed.
* Fixed: WooCommerce picker location plugin when active caused some js conflicts, resolved now.
* Fixed: Variable products discount price range now appearing correctly.
* Fixed: Product saved in campaign rules if deleted cause PHP error, resolved now.


= 2.6.0 (2018-11-11) =
* Added: Compatible with WordPress 5.0
* Added: Hide hours in countdown timer and hide multiple timers settings in finale global settings.
* Improved: Uncode, woodmart, kallayas and traveler theme compatibility added. Some having JS conflicts on single campaign page.
* Improved: Speed optimised: Set post data was saving transient every time when cache was not available.
* Improved: Rules metabox function called only on edit single campaign page.
* Improved: woocommerce manual order create backend ajax call allowed for Finale now, so letting Finale prices to come in manual order creation.
* Improved: Pagination added in campaign listing in wp admin.
* Improved: Action hook added when all classes are initialised.
* Improved: Composite type products compatibility added.
* Improved: Yith woocommerce product bundles compatibility added.
* Fixed: Notice for non numeric value on sale price removed while calculating discount.
* Fixed: Conflict with cmb2 scripts fixed by removing the scripts on finale admin pages.
* Fixed: On sale tag visible on variable products without any discount available issue fixed.


= 2.5.1 (2018-08-21) =
* Added: Ability to add decimal discounts in Finale Campaign Discounts.
* Fixed: Non numeric value warning resolved for PHP 7.1+
* Fixed: Issue with Finale discounts when set discount on sale price and sale price is not set on a product.


= 2.5.0 (2018-08-16) =
* Added: Discount on Regular price and Sale Price options added in a Finale campaign.
* Added: Compatibility for cart item prices added for WooCommerce currency switcher plugin (author: realmag777).
* Added: Compatibility for cart item prices added for WooCommerce Multi currency plugin (author:villatheme).
* Added: Restrict Finale campaigns to run on various admin ajax calls.
* Improved: Handling of Finale prices over the cart to support extra product addon related plugins.
* Improved: Condition added When no Finale campaign or no discount in Finale campaign then return given price.
* Improved: Restrict Finale campaign fields to load on every dashboard page and optimize page lookup.
* Improved: Added handling when product parent doesn't set proper (i.e. 0 or wrong value which is not a product) by any dynamic product creation plugins, that was leading to fatal error.


= 2.4.1 (2018-06-28) =
* Fixed: One PHP notice was coming on auto generated campaigns.


= 2.4.0 (2018-06-27) =
* Security update: Prohibited direct access.
* Added: htaccess file to block access in supportive xl folders inside uploads.


= 2.3.0 (2018-06-20) =
* Added: Admin notice if, in a Finale campaign, the counter bar in enabled but Finale inventory is disabled.
* Added: Reducing back the Finale campaign 'sold unit' on Order's cancellation.
* Added: Finale global settings page introduced; added 2 important settings: Hide days in timer if 0 and Switch Finale positions for builders.
* Improved: Sometimes garbage value saved in post_parent column of product post, that result in PHP error. Now, scenario handled.
* Improved: XLCore loaded early in execution, to avoid PHP errors caused by feeds plugins.
* Improved: Helping text below fields for more clarity.
* Improved: Cache layer added in Finale rules execution.
* Fixed: Fetching Finale campaigns on wc_get_product call causing PHP errors when wc_get_product called on non-products.
* Fixed: WooCommerce Multilingual plugin in recent release v4.3 modified their code that caused PHP error in Finale. Fixed now.
* Fixed: Increasing Finale campaign sold units hooked later in the code, to avoid the un-necessary increase for pending orders.


= 2.2.1 (2018-06-11) =
* Improved: Some strings were left for translations, now done.
* Fixed: Issue detected with ALI Drop Shipping Woo plugin, instead of returning an array on filter hook returning a blank value.


= 2.2.0 (2018-05-29) =
* Added: language pot file.
* Fixed: Variations prices are not coming with the discount in the price HTML, occurred after WC 3.4 update.
* Improved: Code optimization done in Product rules.
* Improved: Restrict display of countdown timer to one on the single product page if multiple exist.
* Improved: wp_cache_flush function calls removed and code optimized to support caching.


= 2.1.0 (2018-03-31) =
* Added: Logging errors in case site faced any PHP Error.
* Added: Admin notification when plugin update is available.
* Added: Force plugin transient removal and optin reset options added in xlplugins -> tools
* Improved: Product rules: category, tags & attributes code updated.
* Improved: Group field: added 'id' attribute as some plugin required id as when postbox class exist on an element.
* Fixed: Force clear transients on Finale campaign save, delete, activate or deactivate.


= 2.0.0 (2018-03-07) =
* Improved: Query optimization when setting up data and when parsing product category rules.
* Improved: Speeds up countdown timer initialization.
* Fixed: Bug in rules parsing, returning true for any true rule in the group.
* Improved: Compatibility Code updated for the WooCommerce TM Extra Product Add-ons. There was an edge case when option prices are zero hence, no finale price getting applied to the cart.


= 1.9.0 (2018-02-23) =
* Added: Used WordPress File System API to save NextMove page and meta queries in files to save db calls.
* Improved: Finale Data setup moved from the_post to 'wc_get_product'. Makes Finale compatible with WP JSON API, WC API, and many third party plugins.
* Added: Flushing object cache when Finale Campaign modified.
* Improved: Campaign ordering updating in all the admin screens. Now order-by "campaign priority," previously order by "date created."
* Improved: Reducing Finale inventory in case order status pending-payment or 'failed' or 'cancelled'.
* Fixed: Quick View HTML corrected for the deactivated campaigns.
* Added: API static function to get dynamic discount prices of the product.
* Added: Fatal Error logging is on to track fatal errors on the site.
* Added: New field added in Countdown Timer to modify size on Mobile.
* Fixed: Single Campaign admin page, showing status 'Deactivated' in case campaign is Deactivated in quick view.
* Improved: Porto theme modified their price calling code, compatibility updated.
* Added: Compatibility for Astra theme.
* Added: Compatibility with LearnDash Addon
* Added: Compatibility with Google Feed Manager plugin.


= 1.8.0 (2018-02-03) =
* Fixed: Compatibility of Finale discounts with Variable Subscription Products.
* Fixed: Issue of product meta not setting up properly when inventory custom qty is 0.
* Improved: Activate or De-activate campaign button added on single campaign page in sidebar.
* Added: Compatible with WooCommerce 3.3 and 3.3.1
* Added: Compatibility of Finale campaigns with Flatsome theme quick view feature.
* Added: Compatibility of Finale campaigns inventory with WooCommerce Subscription products.
* Improved: TM Extra Product Options and WooCommerce Deposit plugins, compatibilities code modified.


= 1.7.1 (2018-01-18) =
* Fixed: PHP fatal error on the cart when WooCommerce TM Extra Product Options Plugin is activated.
* Added: Compatibility with 'Min and Max Quantity for WooCommerce' plugin.
* Added: Compatibility with Aelia Currency Switcher Addon.


= 1.7.0 (2018-01-09) =
* Added: meta no-index for WCCT Campaign post type for search engines
* Improved: Added google index off tag on WCCT Header info data to not index for Google Search Console.
* Improved: Display Countdown Timer upfront with 00 days 00 hrs 00 mins until the actual left time came from ajax response.
* Improved: Query optimisation for variable products. Removed calling of wc_get_product function for variations while creating variable product price range.
* Added: Setting to not run inventory campaigns over out of stock products are now being taken care at variation level. Initially, it was working for the variable product as a whole.
* Fixed: When Finale modifies stock attributes like stock status and quantity using filters, it was getting saved in the actual product meta after checkout & hence overriding the product's actual state. We prevent making any change in the database by removing our filters not to run while stock getting reduced.
* Improved: Queries to get and set product meta for their initial stock state only recorded once per campaign run. Reduced the update post meta queries.
* Improved: All the get product meta queries get cached using XL_Cache for the inventory part.


= 1.6.0 (2018-01-02) =
* Added: Compatibility with php 7.2
* Added: Upgraded to CMB2 2.3.0
* Added: Compatibility with Tucson theme.
* Added: Compatible with Techmarket theme.
* Added: Compatibility with WC Deposits plugin.


= 1.5.1 (2017-12-08) =
* Fixed: Fatal error while activating the plugin of undeclared class XL_transients


= 1.5.0 (2017-12-07) =
* Added: Campaign start date and end date merge tags added.
* Improved: Optimized Some queries(wp query and get queries) that were duplicating, now cached.
* Added: Compatibility with WooCommerce TM Extra Product Options Plugin.
* Added: Compatible with Boxshop theme.
* Fixed: Taking over the charge for the WooCoommerce setting for stock hold in case of finale is active, this critical bug was preventing users from checkout successfully.
* Fixed: Restricted Finale campaigns to run in the backend, causing change for price and inventory in product listing in the backend.
* Fixed: X-Store theme compatibility, there was a PHP warning showing because of a typo left.
* Added: Full Compatibility with qTranslateX plugin.
* Added: Full Compatibility with WooCommerce Currency Switcher By realmag777
* Improved: Displaying 'Expires on Date Time' in campaign listing view for one-time campaigns.


= 1.4.3 (2017-11-17) =
* Added: Compatible with WordPress 4.9
* Fixed: Countdown Timer not re-initiating in case of cache plugins.
* Improved: Reduce number of requests on frontend. Minified and Combined public css and js files.


= 1.4.2 (2017-11-08) =
* Fixed: Timer is getting refreshed on wc_fragment_refreshed function, sometimes results in starting back again.
* Added: admin css for rules as some themes override calling of chosen.
* Improved: Data setup improved to be compatible with the case when not all variations loaded on load of page but by AJAX.
* Improved: Set Priority to 0 in case priority is not set.


= 1.4.1 (2017-10-29) =
* Fixed: warnings coming on wcct_merge_tags function due to typecasting, corrected now


= 1.4.0 (2017-10-29) =
* Added: Themes (Oceanwp, Basel, Enfold, Porto, Revo, Aurum, Savoy, Sober, TheGem) support added for single product page positions.
* Improved: wcct_set_global_data function has 1 notice on do action, corrected now.
* Removed: display_shows_options function conflicting with few themes, now removed.
* Improved: timezone setting code on various occasions (merge tags, rule builder etc.) changed.


= 1.3.4 =
* Added: Campaign duplicator functionality added.
* Improved: Optimization in countdown timer refresh logic to prevent multiple calls for one single campaign.
* Added: Compatibility with WooCommerce Products Bundle Addons.


= 1.3.3 =
* Fixed: counter bar text not displaying if no counter_bar merge tag added
* Fixed: WordPress native transient function replaced with custom transient function. Issue occurred with cache plugins.


= 1.3.2 =
* Added: Not now and No thanks option in Notices.
* Fixed: Single notice on finale pages.


= 1.3.1 =
* Fixed: Issue in initialization of xl menu, causing licensing related errors for the other xl addons.
* Improved: Optimized deactivation pop up functionality to make this process fast.


= 1.3.0 =
* Fixed: Reset timer after page load by firing ajax; replacing data-left & timers;
* Fixed: Timer picker css conflict
* Added: TheGem theme support added for single product.


= 1.2.2 =
* Fixed: jQuery.confirm library enqueued
* Fixed: Issues with multiple inventory bars in one product page.
* Fixed: Campaign priority is not working.
* Improved: Holding transients to not work for now.


= 1.2.1 =

* Fixed: PHP notice on cart page.
* Added: WPML Compatibility against transients used in plugin.
* Added: Theme Compatibility with oxygen theme.
* Added: Minified JS for admin screens added.
* Fixed: Critial bug of javascript conflict with chosen JS resolved.
* Improvement: Changes in filter calling of element over grids so that it would read enable/disable settings of a campaign.


= 1.2.0 =

* Added: Notices in admin area on top of edit campaign page to warn administrators about caching.
* Added: the_content filter hook replaced with wcct_the_content filter hook
* Added: Removed sale_date_to & sale_date_from functions, not being able to make it work for variations products.


= 1.1.8 =

* Added: Compatibility with WPML.
* Added: Betheme, Eva theme & Wowmall theme support added.
* Added: Filter hook to skip discounts later by campaign or product attribute.
* Fixed: Date interval calculation was not calculating total days left for admin area.


= 1.1.7 =

 * Fixed: XL Core start file optimized to not fire database request when secenting XL Core.
 * Fixed: Update Campaign meta data calling and improvements in toolbar data.
 * Fixed: Modified logic to sale date start and end for wc sale.
 * Added: New metabox to show campaign info in edit order screen.
 * Fixed: Handled Conflict with caching plugins when updating campaigns.


= 1.1.6 =

* Fixed: Css Bug fixes.


= 1.1.5 =
* Fixed: Handling for the extra HTML placed by WordPress apply_filter('the_content') inside the countdown timer.
* Fixed: Expiration Time of all the cache sets to Zero to avoid any imcompatibility with caching Plugins.


= 1.1.4 =
* Update: Removed PHP notices coming on plugins page in admin area.

= 1.1.3 =
* Update: Xl core updated to 2.0.5

= 1.1.2 =
* Fixed: Critical Issue with persistent cache.
* Fixed: Removal of cache when campaign is saved in backend.

= 1.1.1 =
* Fixed: Critical Issue, cart_item_stock validation to check is_managing_stock before going to check session hold stock.


= 1.1.0 =
* Fixed: Critical Issue, Fixed cart_item_stock check and respect manage_stock according to the WC version, showing error in cart even no campaign for the products not managing stock.
* Fixed: Do not show counter bar if product is found out of stock just before rendering
* Fixed: Marking product is_on_sale if discount is on for all WooCommerce Versions.
* Fixed: `woocommerce_variation_is_in_stock` is now works for all WooCommerce Versions.
* Added: New metabox added , Quick View placed on edit campaign page to show current state of campaign in single view.
* Added: Admin Toolbar menu added, Hovering over this menu will let admin know about the running campaign(s) and lots more info for the respective product.
* Fixed: UI/UX improvements in campaign settings & help like added under each tab.

= 1.0.4 =
* Fixed: Compatibility with WC2.0.X for `woocommerce_product_backorders_allowed` breaking sites.
* Fixed: Fixed Issue of not showing disounted price range for variations.
* Fixed: Campaign order was not working. Fetching campaigns by campaign name.



= 1.0.4 =
* Fixed: Compatibility with WC2.0.X for `woocommerce_product_backorders_allowed` breaking sites.
* Fixed: Fixed Issue of not showing disounted price range for variations.
* Fixed: Campaign order was not working. Fetching campaigns by campaign name.


= 1.0.3 =
* Added: Settings tabs are now sustained, so user will be switched to the tab he was last working on.
* Fixed: Critical bug when user's time is greater than end time of campaign. Making browser window reloading again and again after page load.
* Added: Improvements in post box content, added user's time, timezone and campaign state.



= 1.0.2 =
* Fixed: XL Core updated to v2.0.2- Deactivation survey default option removed from being auto-checked.
* Fixed: Sale price was not getting calculated for the variation price ranges when taxes settings are on in WooCommerce.
* Fixed: Inventory bar was not showing up for the variable products that are not managing stock at prouct level.

= 1.0.1 =
* Fixed: XL Core updated to v2.0.1- Improvements in support tickets generation & deactivation popups
* Fixed: Deactivation pop up data was not getting send.
* Fixed: Logging file getting reset on init function, moved it to wp hook and applied conditions.
* Fixed: Get price discount hooks to return price un modified when its blank (Product is free)


= 1.0.0 =
* Public Release