<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
// Exit if accessed directly
?>
<div class="xl_plugins_wrap">
    <h1><?php _e( 'Manage Licenses', 'xlplugins' ) ?></h1>
	<?php
	if ( is_object( $model ) ) {
		?>
        <div class="wp-filter">
            <ul class="filter-links xl_plugins_license_links">
				<?php $licenses = XL_licenses::get_instance();
				$licenses->get_plugins_list();
				if ( ! empty( $licenses->plugins_list ) ) { ?>
                    <li class="plugin-install-featured <?php echo ( isset( $model->current_tab ) && $model->current_tab == "licenses" ) ? "current" : "" ?>">
                        <a href="<?php echo admin_url( 'admin.php?page=' . $_GET['page'] . "&tab=licenses" ) ?>"><?php _e( 'Licenses', 'xlplugins' ); ?></a>
                    </li>
				<?php } ?>
                <li class="plugin-install-popular <?php echo ( isset( $model->current_tab ) && $model->current_tab == "services" ) ? "current" : "" ?>">
                    <a href="<?php echo admin_url( 'admin.php?page=' . $_GET['page'] . "&tab=support" ) ?>" class=""><?php _e( 'Support', 'xlplugins' ); ?></a>
                </li>
				<?php
				if ( isset( $model->additional_tabs ) && is_array( $model->additional_tabs ) && count( $model->additional_tabs ) > 0 ): ?>
					<?php foreach ( $model->additional_tabs as $tab ): ?>
                        <li class="<?php echo ( isset( $model->current_tab ) && $model->current_tab == $tab['slug'] ) ? "current" : "" ?>">
                            <a href="<?php echo admin_url( 'admin.php?page=' . $_GET['page'] . '&tab=' . $tab['slug'] ) ?>"><?php echo $tab['label']; ?></a>
                        </li>
					<?php endforeach; ?>
				<?php endif; ?>
            </ul>
        </div>
        <br class="clear"/>
        <div id="col-container" class="about-wrap">
            <div class="col-wrap xl_dashboard_license_content">
                <form id="xl_activate-products" method="post" action="" class="validate">
                    <input type="hidden" name="action" value="xl_activate-products"/>
					<?php
					require_once( XL_dashboard::$loader_url . 'includes/class-xl-updater-licenses-table.php' );
					$table       = new XL_Updater_Licenses_Table();
					$table->data = $model->licenses;

					$table->prepare_items();
					$table->display();
					?>
                    <p class="submit woothemes-helper-submit-wrapper">
						<?php
						submit_button( __( 'Activate License', 'xlplugins' ), 'button-primary', null, false );
						?>
                    </p><!--/.submit-->
					<?php wp_nonce_field( 'wt-helper-activate-license', 'xlplugins' ); ?>
                </form>

                <div class="col-wrap">
					<?php
					$notice_text_raw = __( 'Note: You need to have valid license key to receive updates for these plugins. Get Your License Key.', 'xlplugins' );
					$notice_text_raw .= ' <a target="_blank" href="https://xlplugins.com/login/">';
					$notice_text_raw .= __( 'Click Here', 'xlplugins' );
					$notice_text_raw .= '</a>';
					$notice_text     = apply_filters( 'xl_after_license_table_notice', $notice_text_raw );
					?>
                    <p style="font-size: 12px;"><?php echo $notice_text; ?></p>
                </div>
				<?php do_action( 'xl_license_after_notice_wrap' ); ?>
            </div><!--/.col-wrap-->
            <div class="xl-area-right">  <?php do_action( 'xl_licenses_right_content' ); ?> </div>
        </div><!--/#col-container-->
		<?php do_action( 'xl_licenses_after_area' ); ?>
	<?php } else { ?>
        <div class="xl_cannot_connect"> <?php echo apply_filters( 'xl_no_data_license', __( 'Could not connect to server.', 'xlplugins' ) ); ?></div>
	<?php } ?>
</div>