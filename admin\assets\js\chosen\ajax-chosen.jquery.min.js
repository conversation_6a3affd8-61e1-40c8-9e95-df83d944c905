!function(a){a.fn.xlAjaxChosen=function(b,c,d){var e,f,g,h;return null==b&&(b={}),null==d&&(d={}),f={minTermLength:3,afterTypeDelay:500,j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:"term",keepTypingMsg:"Keep typing...",lookingForMsg:"Looking for"},h=this,e=null,g=a.extend({},f,a(h).data(),b),this.xlChosen(d||{}),this.each(function(){return a(this).next(".chosen-container").find(".search-field > input, .chosen-search > input").bind("keyup",function(){cmb2_select_last_interact=a(this).parents(".chosen-container").prev("select");var d,f,j,k;if(j=a(this).val(),k=a.trim(a(this).val()),f=k.length<g.minTermLength?g.keepTypingMsg:g.lookingForMsg+" '"+k+"'",h.next(".chosen-container").find(".no-results").text(f),0===k.length){selected_values_pre=[],h.find("option").each(function(){return a(this).is(":selected")?selected_values_pre.push(a(this).val()+"-"+a(this).text()):a(this).remove()});var l=[];return void 0!==h.attr("data-pre-data")&&(l=JSON.parse(h.attr("data-pre-data"))),console.log(h.html()),a.each(l,function(b,c){if("string"==typeof c?(value=i,text=c):(value=c.value,text=c.text),-1===a.inArray(value+"-"+text,selected_values_pre))return a("<option />").attr("value",value).html(text).appendTo(h)}),h.trigger("chosen:updated"),!1}return k!==a(this).data("prevVal")&&(a(this).data("prevVal",k),this.timer&&clearTimeout(this.timer),!(k.length<g.minTermLength)&&(d=a(this),null==g.data&&(g.data={}),g.data[g.jsonTermKey]=k,null!=g.dataCallback&&(g.data=g.dataCallback(g.data)),g.success,g.success=function(e){h=cmb2_select_last_interact,console.log(e);var f,g,i;if(null!=e)return i=[],h.find("option").each(function(){return a(this).is(":selected")?i.push(a(this).val()+"-"+a(this).text()):a(this).remove()}),h.find("optgroup:empty").each(function(){return a(this).remove()}),f=null!=c?c(e,d):e,g=0,a.each(f,function(b,c){var d,e,f;return g++,c.group?(d=h.find("optgroup[label='"+c.text+"']"),d.size()||(d=a("<optgroup />")),d.attr("label",c.text).appendTo(h),a.each(c.items,function(b,c){var e,f;if("string"==typeof c?(f=b,e=c):(f=c.value,e=c.text),-1===a.inArray(f+"-"+e,i))return a("<option />").attr("value",f).html(e).appendTo(d)})):("string"==typeof c?(f=b,e=c):(f=c.value,e=c.text),-1===a.inArray(f+"-"+e,i)?a("<option />").attr("value",f).html(e).appendTo(h):void 0)}),g?h.trigger("chosen:updated"):(h.data().chosen.no_results_clear(),h.data().chosen.no_results(d.val())),null!=b.success&&b.success(e),d.val(j)},this.timer=setTimeout(function(){return e&&e.abort(),e=a.ajax(g)},g.afterTypeDelay)))})})}}(jQuery);