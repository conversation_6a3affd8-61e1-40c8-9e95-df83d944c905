/**
 * CMB2 Tabs Styling
 */

/*--------------------------------------------------------------
Helper
--------------------------------------------------------------*/
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0
}

.clearfix {
    display: inline-block
}

* html .clearfix {
    height: 1%
}

.clearfix {
    display: block
}

/*--------------------------------------------------------------
Base style
--------------------------------------------------------------*/
.cmb-tabs .cmb-th label {
    color: #555;
    font-size: 12px
}

.cmb-tabs .cmb-type-group .cmb-row, .cmb-tabs .cmb2-postbox .cmb-row {
    margin: 0 0 .8em;
    padding: 0 0 .8em
}

.cmb-tabs span.cmb2-metabox-description {
    display: block
}

.cmb-tabs .cmb-remove-row-button {
    background-color: #e60000;
    border: medium none;
    border-radius: 25px;
    color: #fff;
    height: 20px;
    padding: 0;
    text-indent: -999em;
    width: 20px;
    position: relative;
    -webkit-box-shadow: none;
    box-shadow: none
}

.cmb-tabs .cmb-repeat-row {
    position: relative
}

.cmb-tabs .cmb-remove-row {
    display: inline;
    margin: 0;
    padding: 0
}

.cmb-tabs .cmb-repeat-row .cmb-td {
    display: inline-block
}

/*--------------------------------------------------------------
CMB2 Tabs
--------------------------------------------------------------*/
.cmb-tabs {
    margin: -6px -12px -12px;
    overflow: hidden
}

.cmb-tabs ul.wcct-cmb-tab-nav:after {
    background-color: #fafafa;
    border-right: 1px solid #eee;
    bottom: -9999em;
    content: "";
    display: block;
    position: absolute;
    height: auto;
    left: auto;
    width: 1px;
    top: 0;
    right: 0
}

.cmb-tabs ul.wcct-cmb-tab-nav {
    background-color: #fafafa;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    line-height: 1em;
    margin: 0;
    padding: 0;
    position: relative;
    width: 20%;
    float: left
}

.cmb-tabs ul.wcct-cmb-tab-nav li {
    display: block;
    margin: 0;
    padding: 0;
    position: relative
}

.cmb-tabs i, .cmb-tabs i:before {
    font-size: 16px;
    vertical-align: middle
}

.cmb-tabs ul.wcct-cmb-tab-nav li a {
    border-right: 1px solid #eee;
    border-left: 2px solid #fafafa;
    -webkit-box-shadow: none;
    box-shadow: none;
    display: block;
    line-height: 20px;
    margin: 0;
    padding: 10px;
    text-decoration: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: 600
}

.cmb-tabs ul.wcct-cmb-tab-nav li i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.cmb-tabs ul.wcct-cmb-tab-nav li i, .cmb-tabs ul.wcct-cmb-tab-nav li img {
    padding: 0 5px 0 0
}

.cmb-tabs ul.wcct-cmb-tab-nav li a {
    color: #555;
    border: 1px solid transparent
}

.cmb-tabs ul.wcct-cmb-tab-nav li.cmb-tab-active a {
    background-color: #fff;
    position: relative;
    border: 1px solid #eee;
    border-left: 3px solid #00a0d2;
    border-right-color: #fff
}

.cmb-tabs ul.wcct-cmb-tab-nav li:first-of-type.cmb-tab-active a {
    border-top: none
}

.cmb-tabs .cmb-tabs-panel {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #555;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 80%;
    padding: 0
}

.cmb-tabs .cmb2-metabox {
    display: block;
    width: 100%
}

.cmb-tabs .cmb-th {
    width: 18%
}

.cmb-tabs .cmb-th, .cmb-tabs .cmb-td {
    padding: 0 2%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.cmb-tabs .cmb-th + .cmb-td, .cmb-tabs .cmb-th + .cmb-td {
    float: right;
    width: 82%
}

.cmb2-wrap-tabs .cmb-tab-panel {
    display: none
}

.cmb2-wrap-tabs .cmb-tab-panel.show {
    display: block
}

/*--------------------------------------------------------------
Classic Tab
--------------------------------------------------------------*/
.cmb-tabs.cmb-tabs-classic ul.wcct-cmb-tab-nav {
    width: 100%;
    float: none;
    background-color: #fafafa;
    border-right: medium none;
    padding: 0;
    border-bottom: 1px solid #dedede;
    padding-top: 15px
}

.cmb-tabs.cmb-tabs-classic .wcct-cmb-tab-nav li {
    background: #ebebeb none repeat scroll 0 0;
    margin: 0 5px -1px;
    display: inline-block
}

.cmb-tabs.cmb-tabs-classic .wcct-cmb-tab-nav li:first-of-type {
    margin-left: 18px
}

.cmb-tabs.cmb-tabs-classic ul.wcct-cmb-tab-nav::after {
    display: none
}

.cmb-tabs.cmb-tabs-classic .cmb-tabs-panel {
    width: 100%
}

.cmb-tabs.cmb-tabs-classic .cmb-tab-panel {
    padding-top: 10px
}

.cmb-tabs.cmb-tabs-classic ul.wcct-cmb-tab-nav li a {
    padding: 8px 12px;
    background-color: #fafafa;
    border: none;
    border-bottom: 1px solid #dedede
}

.cmb-tabs.cmb-tabs-classic ul.wcct-cmb-tab-nav li.cmb-tab-active a {
    background-color: #fff;
    border-color: #fff;
    border: none;
    border-top: 2px solid #00a0d2;
    border-bottom: 1px solid #fff
}

/*--------------------------------------------------------------
Media Query
--------------------------------------------------------------*/
@media (max-width: 750px) {
    .cmb-tabs ul.wcct-cmb-tab-nav {
        width: 10%
    }

    .cmb-tabs .cmb-tabs-panel {
        width: 90%
    }

    .cmb-tabs ul.wcct-cmb-tab-nav li i, .cmb-tabs ul.wcct-cmb-tab-nav li img {
        padding: 0;
        margin: 0 auto;
        text-align: center;
        display: block;
        max-width: 25px
    }

    .cmb-tabs ul.wcct-cmb-tab-nav li span {
        padding: 10px;
        position: relative;
        text-indent: -999px;
        display: none
    }
}

@media (max-width: 500px) {
    .cmb-tabs .cmb-th, .cmb-tabs .cmb-th + .cmb-td, .cmb-tabs .cmb-th + .cmb-td {
        float: none;
        width: 96%
    }

    .cmb-tabs .cmb-repeat-row .cmb-td {
        width: auto
    }
}
