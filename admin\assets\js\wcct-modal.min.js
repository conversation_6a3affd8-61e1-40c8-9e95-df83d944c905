function wcct_modal_init(a){jQuery("body").on("click",a,wcct_modal_click).on("wcctmodal:iframe:loaded",function(){jQuery("#WCCT_MB_window").removeClass("wcctmodal-loading")})}function wcct_modal_click(){return wcct_modal_show(this.title||this.name||null,this.href||this.alt,this.rel||!1),this.blur(),!1}function wcct_modal_show(a,b,c){var d;try{void 0===document.body.style.maxHeight?(jQuery("body","html").css({height:"100%",width:"100%"}),jQuery("html").css("overflow","hidden"),null===document.getElementById("WCCT_MB_HideSelect")&&(jQuery("body").append("<iframe id='WCCT_MB_HideSelect'>"+wcctmodal10n.noiframes+"</iframe><div id='WCCT_MB_overlay'></div><div id='WCCT_MB_window' class='wcctmodal-loading'></div>"),jQuery("#WCCT_MB_overlay").click(wcct_modal_remove))):null===document.getElementById("WCCT_MB_overlay")&&(jQuery("body").append("<div id='WCCT_MB_overlay'></div><div id='WCCT_MB_window' class='wcctmodal-loading'></div>"),jQuery("#WCCT_MB_overlay").click(wcct_modal_remove),jQuery("body").addClass("modal-open")),wcct_modal_detectMacXFF()?jQuery("#WCCT_MB_overlay").addClass("WCCT_MB_overlayMacFFBGHack"):jQuery("#WCCT_MB_overlay").addClass("WCCT_MB_overlayBG"),null===a&&(a=""),jQuery("body").append("<div id='WCCT_MB_load'><img src='"+imgLoader.src+"' width='208' /></div>"),jQuery("#WCCT_MB_load").show();var e;e=-1!==b.indexOf("?")?b.substr(0,b.indexOf("?")):b;var f=/\.jpg$|\.jpeg$|\.png$|\.gif$|\.bmp$/,g=e.toLowerCase().match(f);if(".jpg"==g||".jpeg"==g||".png"==g||".gif"==g||".bmp"==g){if(WCCT_MB_PrevCaption="",WCCT_MB_PrevURL="",WCCT_MB_PrevHTML="",WCCT_MB_NextCaption="",WCCT_MB_NextURL="",WCCT_MB_NextHTML="",WCCT_MB_imageCount="",WCCT_MB_FoundURL=!1,c)for(WCCT_MB_TempArray=jQuery("a[rel="+c+"]").get(),WCCT_MB_Counter=0;WCCT_MB_Counter<WCCT_MB_TempArray.length&&""===WCCT_MB_NextHTML;WCCT_MB_Counter++){WCCT_MB_TempArray[WCCT_MB_Counter].href.toLowerCase().match(f);WCCT_MB_TempArray[WCCT_MB_Counter].href!=b?WCCT_MB_FoundURL?(WCCT_MB_NextCaption=WCCT_MB_TempArray[WCCT_MB_Counter].title,WCCT_MB_NextURL=WCCT_MB_TempArray[WCCT_MB_Counter].href,WCCT_MB_NextHTML="<span id='WCCT_MB_next'>&nbsp;&nbsp;<a href='#'>"+wcctmodal10n.next+"</a></span>"):(WCCT_MB_PrevCaption=WCCT_MB_TempArray[WCCT_MB_Counter].title,WCCT_MB_PrevURL=WCCT_MB_TempArray[WCCT_MB_Counter].href,WCCT_MB_PrevHTML="<span id='WCCT_MB_prev'>&nbsp;&nbsp;<a href='#'>"+wcctmodal10n.prev+"</a></span>"):(WCCT_MB_FoundURL=!0,WCCT_MB_imageCount=wcctmodal10n.image+" "+(WCCT_MB_Counter+1)+" "+wcctmodal10n.of+" "+WCCT_MB_TempArray.length)}imgPreloader=new Image,imgPreloader.onload=function(){function d(){return jQuery(document).unbind("click",d)&&jQuery(document).unbind("click",d),jQuery("#WCCT_MB_window").remove(),jQuery("body").append("<div id='WCCT_MB_window'></div>"),wcct_modal_show(WCCT_MB_PrevCaption,WCCT_MB_PrevURL,c),!1}function e(){return jQuery("#WCCT_MB_window").remove(),jQuery("body").append("<div id='WCCT_MB_window'></div>"),wcct_modal_show(WCCT_MB_NextCaption,WCCT_MB_NextURL,c),!1}imgPreloader.onload=null;var f=wcct_modal_getPageSize(),g=f[0]-150,h=f[1]-150,i=imgPreloader.width,j=imgPreloader.height;i>g?(j*=g/i,i=g,j>h&&(i*=h/j,j=h)):j>h&&(i*=h/j,j=h,i>g&&(j*=g/i,i=g)),WCCT_MB_WIDTH=i+30,WCCT_MB_HEIGHT=j+60,jQuery("#WCCT_MB_window").append("<a href='' id='WCCT_MB_ImageOff'><span class='screen-reader-text'>"+wcctmodal10n.close+"</span><img id='WCCT_MB_Image' src='"+b+"' width='"+i+"' height='"+j+"' alt='"+a+"'/></a><div id='WCCT_MB_caption'>"+a+"<div id='WCCT_MB_secondLine'>"+WCCT_MB_imageCount+WCCT_MB_PrevHTML+WCCT_MB_NextHTML+"</div></div><div id='WCCT_MB_closeWindow'><button type='button' id='WCCT_MB_closeWindowButton'><span class='screen-reader-text'>"+wcctmodal10n.close+"</span><span class='wcct_modal_close_btn'></span></button></div>"),jQuery("#WCCT_MB_closeWindowButton").click(wcct_modal_remove),""!==WCCT_MB_PrevHTML&&jQuery("#WCCT_MB_prev").click(d),""!==WCCT_MB_NextHTML&&jQuery("#WCCT_MB_next").click(e),jQuery(document).bind("keydown.wcctmodal",function(a){return 27==a.which?wcct_modal_remove():190==a.which?""!=WCCT_MB_NextHTML&&(jQuery(document).unbind("wcctmodal"),e()):188==a.which&&""!=WCCT_MB_PrevHTML&&(jQuery(document).unbind("wcctmodal"),d()),!1}),wcct_modal_position(),jQuery("#WCCT_MB_load").remove(),jQuery("#WCCT_MB_ImageOff").click(wcct_modal_remove),jQuery("#WCCT_MB_window").css({visibility:"visible"})},imgPreloader.src=b}else{var h=b.replace(/^[^\?]+\??/,""),i=wcct_modal_parseQuery(h);if(WCCT_MB_WIDTH=1*i.width+30||630,WCCT_MB_HEIGHT=1*i.height+40||440,ajaxContentW=WCCT_MB_WIDTH-30,ajaxContentH=WCCT_MB_HEIGHT-45,-1!=b.indexOf("WCCT_MB_iframe")?(urlNoQuery=b.split("WCCT_MB_"),jQuery("#WCCT_MB_iframeContent").remove(),"true"!=i.modal?jQuery("#WCCT_MB_window").append("<div id='WCCT_MB_title'><div id='WCCT_MB_ajaxWindowTitle'>"+a+"</div><div id='WCCT_MB_closeAjaxWindow'><button type='button' id='WCCT_MB_closeWindowButton'><span class='screen-reader-text'>"+wcctmodal10n.close+"</span><span class='wcct_modal_close_btn'></span></button></div></div><iframe frameborder='0' hspace='0' allowtransparency='true' src='"+urlNoQuery[0]+"' id='WCCT_MB_iframeContent' name='WCCT_MB_iframeContent"+Math.round(1e3*Math.random())+"' onload='wcct_modal_showIframe()' style='width:"+(ajaxContentW+29)+"px;height:"+(ajaxContentH+17)+"px;' >"+wcctmodal10n.noiframes+"</iframe>"):(jQuery("#WCCT_MB_overlay").unbind(),jQuery("#WCCT_MB_window").append("<iframe frameborder='0' hspace='0' allowtransparency='true' src='"+urlNoQuery[0]+"' id='WCCT_MB_iframeContent' name='WCCT_MB_iframeContent"+Math.round(1e3*Math.random())+"' onload='wcct_modal_showIframe()' style='width:"+(ajaxContentW+29)+"px;height:"+(ajaxContentH+17)+"px;'>"+wcctmodal10n.noiframes+"</iframe>"))):"visible"!=jQuery("#WCCT_MB_window").css("visibility")?"true"!=i.modal?jQuery("#WCCT_MB_window").append("<div id='WCCT_MB_title'><div id='WCCT_MB_ajaxWindowTitle'>"+a+"</div><div id='WCCT_MB_closeAjaxWindow'><a href='#' id='WCCT_MB_closeWindowButton'><div class='wcct_modal_close_btn'></div></a></div></div><div id='WCCT_MB_ajaxContent' style='width:"+ajaxContentW+"px;height:"+ajaxContentH+"px'></div>"):(jQuery("#WCCT_MB_overlay").unbind(),jQuery("#WCCT_MB_window").append("<div id='WCCT_MB_ajaxContent' class='WCCT_MB_modal' style='width:"+ajaxContentW+"px;height:"+ajaxContentH+"px;'></div>")):(jQuery("#WCCT_MB_ajaxContent")[0].style.width=ajaxContentW+"px",jQuery("#WCCT_MB_ajaxContent")[0].style.height=ajaxContentH+"px",jQuery("#WCCT_MB_ajaxContent")[0].scrollTop=0,jQuery("#WCCT_MB_ajaxWindowTitle").html(a)),jQuery("#WCCT_MB_closeWindowButton").click(wcct_modal_remove),-1!=b.indexOf("WCCT_MB_inline"))jQuery("#WCCT_MB_ajaxContent").append(jQuery("#"+i.inlineId).children()),jQuery("#WCCT_MB_window").bind("wcct_modal_unload",function(){jQuery("#"+i.inlineId).append(jQuery("#WCCT_MB_ajaxContent").children())}),wcct_modal_position(),jQuery("#WCCT_MB_load").remove(),jQuery("#WCCT_MB_window").css({visibility:"visible"});else if(-1!=b.indexOf("WCCT_MB_iframe"))wcct_modal_position(),jQuery("#WCCT_MB_load").remove(),jQuery("#WCCT_MB_window").css({visibility:"visible"});else{var j=b;j+=-1===b.indexOf("?")?"?":"&",jQuery("#WCCT_MB_ajaxContent").load(j+="random="+(new Date).getTime(),function(){wcct_modal_position(),jQuery("#WCCT_MB_load").remove(),wcct_modal_init("#WCCT_MB_ajaxContent a.wcctmodal"),jQuery("#WCCT_MB_window").css({visibility:"visible"})})}}i.modal||jQuery(document).bind("keydown.wcctmodal",function(a){if(27==a.which)return wcct_modal_remove(),!1}),d=jQuery("#WCCT_MB_closeWindowButton"),d.find(".wcct_modal_close_btn").is(":visible")&&d.focus(),jQuery("#WCCT_MB_ajaxContent").innerHeight()>window.innerHeight&&jQuery("#WCCT_MB_ajaxContent").height(90*window.innerHeight/100)}catch(a){}}function wcct_modal_showIframe(){jQuery("#WCCT_MB_load").remove(),jQuery("#WCCT_MB_window").css({visibility:"visible"}).trigger("wcctmodal:iframe:loaded")}function wcct_modal_remove(){return jQuery("#WCCT_MB_imageOff").unbind("click"),jQuery("#WCCT_MB_closeWindowButton").unbind("click"),jQuery("#WCCT_MB_window").fadeOut("fast",function(){jQuery("#WCCT_MB_window, #WCCT_MB_overlay, #WCCT_MB_HideSelect").trigger("wcct_modal_unload").unbind().remove(),jQuery("body").trigger("wcctmodal:removed")}),jQuery("body").removeClass("modal-open"),jQuery("#WCCT_MB_load").remove(),void 0===document.body.style.maxHeight&&(jQuery("body","html").css({height:"auto",width:"auto"}),jQuery("html").css("overflow","")),jQuery(document).unbind(".wcctmodal"),!1}function wcct_modal_position(){var a=void 0===document.body.style.maxHeight;jQuery("#WCCT_MB_window").css({marginLeft:"-"+parseInt(WCCT_MB_WIDTH/2,10)+"px",width:WCCT_MB_WIDTH+"px"}),a||jQuery("#WCCT_MB_window").css({marginTop:"-"+parseInt(WCCT_MB_HEIGHT/2,10)+"px"})}function wcct_modal_parseQuery(a){var b={};if(!a)return b;for(var c=a.split(/[;&]/),d=0;d<c.length;d++){var e=c[d].split("=");if(e&&2==e.length){var f=unescape(e[0]),g=unescape(e[1]);g=g.replace(/\+/g," "),b[f]=g}}return b}function wcct_modal_getPageSize(){var a=document.documentElement,b=window.innerWidth||self.innerWidth||a&&a.clientWidth||document.body.clientWidth,c=window.innerHeight||self.innerHeight||a&&a.clientHeight||document.body.clientHeight;return arrayPageSize=[b,c],arrayPageSize}function wcct_modal_detectMacXFF(){var a=navigator.userAgent.toLowerCase();if(-1!=a.indexOf("mac")&&-1!=a.indexOf("firefox"))return!0}if("string"!=typeof wcct_modal_pathToImage)var wcct_modal_pathToImage="undefined"!=typeof wcctmodal10n?wcctmodal10n.loadingAnimation:"";jQuery(document).ready(function(){wcct_modal_init("a.wcctmodal, area.wcctmodal, input.wcctmodal"),imgLoader=new Image,imgLoader.src=wcct_modal_pathToImage});