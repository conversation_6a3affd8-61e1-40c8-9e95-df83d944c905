/*!
 * The Final Countdown for jQuery v2.2.0 (http://hilios.github.io/jQuery.countdown/)
 */
!function(a){"use strict";"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){"use strict";function b(a){if(a instanceof Date)return a;if(String(a).match(g))return String(a).match(/^[0-9]*$/)&&(a=Number(a)),String(a).match(/\-/)&&(a=String(a).replace(/\-/g,"/")),new Date(a);throw new Error("Couldn't cast `"+a+"` to a date object.")}function c(a){var b=a.toString().replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1");return new RegExp(b)}function d(a){return function(b){var d=b.match(/%(-|!)?[A-Z]{1}(:[^;]+;)?/gi);if(d)for(var f=0,g=d.length;f<g;++f){var h=d[f].match(/%(-|!)?([a-zA-Z]{1})(:[^;]+;)?/),j=c(h[0]),k=h[1]||"",l=h[3]||"",m=null;h=h[2],i.hasOwnProperty(h)&&(m=i[h],m=Number(a[m])),null!==m&&("!"===k&&(m=e(l,m)),""===k&&m<10&&(m="0"+m.toString()),b=b.replace(j,m.toString()))}return b=b.replace(/%%/,"%")}}function e(a,b){var c="s",d="";return a&&(a=a.replace(/(:|;|\s)/gi,"").split(/\,/),1===a.length?c=a[0]:(d=a[0],c=a[1])),Math.abs(b)>1?c:d}var f=[],g=[],h={precision:100,elapse:!1,defer:!1};g.push(/^[0-9]*$/.source),g.push(/([0-9]{1,2}\/){2}[0-9]{4}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),g.push(/[0-9]{4}([\/\-][0-9]{1,2}){2}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),g=new RegExp(g.join("|"));var i={Y:"years",m:"months",n:"daysToMonth",d:"daysToWeek",w:"weeks",W:"weeksToMonth",H:"hours",M:"minutes",S:"seconds",D:"totalDays",I:"totalHours",N:"totalMinutes",T:"totalSeconds"},j=function(b,c,d){this.el=b,this.$el=a(b),this.interval=null,this.offset={},this.options=a.extend({},h),this.instanceNumber=f.length,f.push(this),this.$el.data("countdown-instance",this.instanceNumber),d&&("function"==typeof d?(this.$el.on("update.countdown",d),this.$el.on("stoped.countdown",d),this.$el.on("finish.countdown",d)):this.options=a.extend({},h,d)),this.setFinalDate(c),this.options.defer===!1&&this.start()};a.extend(j.prototype,{start:function(){null!==this.interval&&clearInterval(this.interval);var a=this;this.update(),this.interval=setInterval(function(){a.update.call(a)},this.options.precision)},stop:function(){clearInterval(this.interval),this.interval=null,this.dispatchEvent("stoped")},toggle:function(){this.interval?this.stop():this.start()},pause:function(){this.stop()},resume:function(){this.start()},remove:function(){this.stop.call(this),f[this.instanceNumber]=null,delete this.$el.data().countdownInstance},setFinalDate:function(a){this.finalDate=b(a)},update:function(){if(0===this.$el.closest("html").length)return void this.remove();var b,c=void 0!==a._data(this.el,"events"),d=new Date;b=this.finalDate.getTime()-d.getTime(),b=Math.ceil(b/1e3),b=!this.options.elapse&&b<0?0:Math.abs(b),this.totalSecsLeft!==b&&c&&(this.totalSecsLeft=b,this.elapsed=d>=this.finalDate,this.offset={seconds:this.totalSecsLeft%60,minutes:Math.floor(this.totalSecsLeft/60)%60,hours:Math.floor(this.totalSecsLeft/60/60)%24,days:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToWeek:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToMonth:Math.floor(this.totalSecsLeft/60/60/24%30.4368),weeks:Math.floor(this.totalSecsLeft/60/60/24/7),weeksToMonth:Math.floor(this.totalSecsLeft/60/60/24/7)%4,months:Math.floor(this.totalSecsLeft/60/60/24/30.4368),years:Math.abs(this.finalDate.getFullYear()-d.getFullYear()),totalDays:Math.floor(this.totalSecsLeft/60/60/24),totalHours:Math.floor(this.totalSecsLeft/60/60),totalMinutes:Math.floor(this.totalSecsLeft/60),totalSeconds:this.totalSecsLeft},this.options.elapse||0!==this.totalSecsLeft?this.dispatchEvent("update"):(this.stop(),this.dispatchEvent("finish")))},dispatchEvent:function(b){var c=a.Event(b+".countdown");c.finalDate=this.finalDate,c.elapsed=this.elapsed,c.offset=a.extend({},this.offset),c.strftime=d(this.offset),this.$el.trigger(c)}}),a.fn.wcctCountdown=function(){var b=Array.prototype.slice.call(arguments,0);return this.each(function(){var c=a(this).data("countdown-instance");if(void 0!==c){var d=f[c],e=b[0];j.prototype.hasOwnProperty(e)?d[e].apply(d,b.slice(1)):null===String(e).match(/^[$A-Z_][0-9A-Z_$]*$/i)?(d.setFinalDate.call(d,e),d.start()):a.error("Method %s does not exist on jQuery.countdown".replace(/\%s/gi,e))}else new j(this,b[0],b[1])})}});

function humanized_time_span(a,b,c,d){function e(){for(var a=0;a<c[j].length;a++)if(null==c[j][a].ceiling||i<=c[j][a].ceiling)return c[j][a];return null}function f(){for(var a=i,b={},c=0;c<d.length;c++){var e=Math.floor(a/d[c][0]);a-=d[c][0]*e,b[d[c][1]]=e}return b}function g(a){var b=f();return h(a.text.replace(/\$(\w+)/g,function(){return b[arguments[1]]}),b)}function h(a,b){for(var c in b)if(1==b[c]){var d=new RegExp("\\b"+c+"\\b");a=a.replace(d,function(){return arguments[0].replace(/s\b/g,"")})}return a}c=c||{past:[{ceiling:60,text:"$seconds seconds"},{ceiling:3600,text:"$minutes minutes"},{ceiling:86400,text:"$hours hours"},{ceiling:604800,text:"$days days"},{ceiling:2592e3,text:"$weeks weeks"},{ceiling:31556926,text:"$months months"},{ceiling:null,text:"$years years"}],future:[{ceiling:60,text:"$seconds seconds"},{ceiling:3600,text:"$minutes minutes"},{ceiling:86400,text:"$hours hours"},{ceiling:604800,text:"$days days"},{ceiling:2592e3,text:"$weeks weeks"},{ceiling:31556926,text:"$months months"},{ceiling:null,text:"$years years"}]},d=d||[[31556926,"years"],[2629744,"months"],[604800,"weeks"],[86400,"days"],[3600,"hours"],[60,"minutes"],[1,"seconds"]],a=new Date(a),b=b?new Date(b):new Date;var i=(b-a)/1e3,j="past";return i<0&&(j="future",i=0-i),g(e())}

var wcct_timeOut=!1,wcctAllUniqueTimers=[];!function(a){"use strict";function b(){wcct_data.hasOwnProperty("refresh_timings")&&"no"!==wcct_data.refresh_timings&&a(".wcct_countdown_timer .wcct_timer_wrap").each(function(){var b=a(this).parents(".wcct_countdown_timer").attr("data-campaign-id");wcctAllUniqueTimers.indexOf(b)>-1||(wcctAllUniqueTimers.push(b),d(),a.ajax({url:wcct_data.admin_ajax,type:"GET",dataType:"json",data:{wcct_action:"wcct_refreshed_times",location:document.location.href,endDate:a(this).attr("data-date"),campID:a(this).parents(".wcct_countdown_timer").attr("data-campaign-id")},beforeSend:function(){},success:function(b){a(".wcct_countdown_timer[data-campaign-id='"+b.id+"']").each(function(){var a=jQuery(this).children(".wcct_timer_wrap").attr("data-left");if(0===b.diff)switch(jQuery(this).attr("data-type")){case"counter_bar":jQuery(this).parents(".wcct_counter_bar").eq(0).fadeOut().remove();break;case"single":jQuery(this).eq(0).fadeOut().remove()}else{var c=jQuery(this).attr("data-delay");void 0!==c&&b.diff>parseInt(c)&&jQuery(this).remove(),parseInt(a)-parseInt(b.diff)>10&&(jQuery(this).removeAttr("data-wctimer-load"),jQuery(this).children(".wcct_timer_wrap").attr("data-left",b.diff))}}),d()}}))})}function c(){a("#wp-admin-bar-wcct_admin_page_node-default").length>0&&a("#wp-admin-bar-wcct_admin_page_node-default").html(a(".wcct_header_passed").html())}function d(b){void 0===b&&(b=!0),a(".wcct_timer").length>0&&a(".wcct_timer").each(function(){var c=a(this);if("yes"==c.attr("data-wctimer-load"))return!0;var d,e,f,g,h,i,j,k,l,m,n,o=c.find(".wcct_timer_wrap"),p=(parseInt(o.attr("data-date")),o.attr("data-timer-skin")),q=""!=a(this).attr("data-days")?a("<div>").text(a(this).attr("data-days")).html():"day",r=""!=a(this).attr("data-hrs")?a("<div>").text(a(this).attr("data-hrs")).html():"hr",s=""!=a(this).attr("data-mins")?a("<div>").text(a(this).attr("data-mins")).html():"min",t=""!=a(this).attr("data-secs")?a("<div>").text(a(this).attr("data-secs")).html():"sec",u=""!=a(this).attr("data-is_days")?a("<div>").text(a(this).attr("data-is_days")).html():"yes",v=""!=a(this).attr("data-is-hrs")?a("<div>").text(a(this).attr("data-is-hrs")).html():"yes",w=(new Date).getTime()+1e3*parseInt(o.attr("data-left"));o.wcctCountdown(w,{elapse:!0}).on("update.countdown",function(o){if(e=o.offset.seconds,f=o.offset.minutes,g=o.offset.hours,h=i=j=k=l=m=n="","0"==e&&(h=" wcct_pulse wcct_animated",l=" wcct_border_none"),"0"==e&&"0"==h&&(i=" wcct_pulse wcct_animated",m=" wcct_border_none"),"0"==e&&"0"==h&&"0"==i&&(j=" wcct_pulse wcct_animated",n=" wcct_border_none"),d="",o.elapsed&&1==b){var w=c.parents(".wcct_header_area");w.length>0&&(w.find(".wcct_close").trigger("click"),setTimeout(function(){w.remove()},1e3));var x=c.parents(".wcct_footer_area");if(x.length>0&&(x.find(".wcct_close").trigger("click"),setTimeout(function(){x.remove()},1e3)),setTimeout(function(){c.remove()},1e3),!1===wcct_timeOut&&(a.ajax({url:wcct_data.admin_ajax,type:"POST",dataType:"json",data:{action:"wcct_clear_cache"},success:function(a){},timeout:10}),"yes"==wcct_data.reload_page_on_timer_ends)){setTimeout(function(){window.location.reload()},2e3)}}else{var y="%D",z="%H",A="%M",B="%S";0==b&&(y="00",z="00",A="00",B="00"),"round_fill"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_round_wrap '+n+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+'</div></div><div class="wcct_wrap_border '+j+'"></div></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_round_wrap '+m+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+'</div></div><div class="wcct_wrap_border '+i+'"></div></div>'),d+='<div class="wcct_round_wrap '+l+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div><div class="wcct_wrap_border '+h+'"></div></div><div class="wcct_round_wrap wcct_border_none"><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+'</div></div><div class="wcct_wrap_border wcct_pulse wcct_animated"></div></div>'):"round_ghost"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_round_wrap '+n+'"><div class="wcct_wrap_border '+j+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+"</div></div></div>"),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_round_wrap '+m+'"><div class="wcct_wrap_border '+i+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+"</div></div></div>"),d+='<div class="wcct_round_wrap '+l+'"><div class="wcct_wrap_border '+h+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div></div><div class="wcct_round_wrap wcct_border_none"><div class="wcct_wrap_border wcct_pulse wcct_animated"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+"</div></div></div>"):"square_fill"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_square_wrap '+n+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+'</div></div><div class="wcct_wrap_border '+j+'"></div></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_square_wrap '+m+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+'</div></div><div class="wcct_wrap_border '+i+'"></div></div>'),d+='<div class="wcct_square_wrap '+l+'"><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div><div class="wcct_wrap_border '+h+'"></div></div><div class="wcct_square_wrap wcct_border_none"><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+'</div></div><div class="wcct_wrap_border wcct_pulse wcct_animated"></div></div>'):"square_ghost"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_square_wrap '+n+'"><div class="wcct_wrap_border '+j+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+y+"</span> "+q+"</div></div></div>"),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_square_wrap '+m+'"><div class="wcct_wrap_border '+i+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+z+"</span> "+r+"</div></div></div>"),d+='<div class="wcct_square_wrap '+l+'"><div class="wcct_wrap_border '+h+'"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+A+"</span> "+s+'</div></div></div><div class="wcct_square_wrap wcct_border_none"><div class="wcct_wrap_border wcct_pulse wcct_animated"></div><div class="wcct_table"><div class="wcct_table_cell"><span>'+B+"</span> "+t+"</div></div></div>"):"highlight_1"==p?((o.offset.totalDays>0||"yes"==u)&&(d='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+y+"</span> "+q+'<span class="wcct_colon_sep">:</span></div>'),(o.offset.totalHours>0||"yes"==v)&&(d+='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+z+"</span> "+r+'<span class="wcct_colon_sep">:</span></div>'),d+='<div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+A+"</span> "+s+'<span class="wcct_colon_sep">:</span></div><div class="wcct_highlight_1_wrap"><span class="wcct_timer_label">'+B+"</span> "+t+"</div>"):((o.offset.totalDays>0||"yes"==u)&&(d=y+q),(o.offset.totalHours>0||"yes"==v)&&(d+=" "+z+r),d+=" "+A+s+" "+B+t),a(this).html(o.strftime(d))}}),c.attr("data-wctimer-load","yes")})}function e(){a(".wcct_counter_bar").length>0&&a(".wcct_counter_bar").each(function(){var b=a(this);if(b.css("display","block"),b.find(".wcct_progress_aria").length>0){var c=b.find(".wcct_progress_aria");if(c.visible(!0)&&!c.hasClass("wcct_bar_active")){c.addClass("wcct_bar_active");var d=c.find(".wcct_progress_bar").attr("aria-valuenow");setTimeout(function(){c.find(".wcct_progress_bar").css("width",d+"%")},200)}}})}a(".variations_form").on("woocommerce_variation_select_change",function(){}),a(".variations_form").on("show_variation",function(a,b){}),a(document).ready(function(){b(),d(),e(),c()}),a(window).scroll(function(){e()}),a(document).bind("wc_fragments_refreshed",function(){d()})}(jQuery);

!function(a){var b=a(window);a.fn.visible=function(a,c,d){if(!(this.length<1)){var e=this.length>1?this.eq(0):this,f=e.get(0),g=b.width(),h=b.height(),d=d||"both",i=!0!==c||f.offsetWidth*f.offsetHeight;if("function"==typeof f.getBoundingClientRect){var j=f.getBoundingClientRect(),k=j.top>=0&&j.top<h,l=j.bottom>0&&j.bottom<=h,m=j.left>=0&&j.left<g,n=j.right>0&&j.right<=g,o=a?k||l:k&&l,p=a?m||n:m&&n;if("both"===d)return i&&o&&p;if("vertical"===d)return i&&o;if("horizontal"===d)return i&&p}else{var q=b.scrollTop(),r=q+h,s=b.scrollLeft(),t=s+g,u=e.offset(),v=u.top,w=v+e.height(),x=u.left,y=x+e.width(),z=!0===a?w:v,A=!0===a?v:w,B=!0===a?y:x,C=!0===a?x:y;if("both"===d)return!!i&&r>=A&&z>=q&&t>=C&&B>=s;if("vertical"===d)return!!i&&r>=A&&z>=q;if("horizontal"===d)return!!i&&t>=C&&B>=s}}}}(jQuery);